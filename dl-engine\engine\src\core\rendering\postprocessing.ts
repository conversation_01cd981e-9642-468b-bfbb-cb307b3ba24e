/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import * as THREE from 'three'

/**
 * 后处理效果类型
 */
export enum PostProcessingEffect {
  BLOOM = 'bloom',
  SSAO = 'ssao',
  FXAA = 'fxaa',
  TONE_MAPPING = 'toneMapping',
  COLOR_CORRECTION = 'colorCorrection',
  DEPTH_OF_FIELD = 'depthOfField',
  MOTION_BLUR = 'motionBlur',
  OUTLINE = 'outline'
}

/**
 * 后处理配置接口
 */
export interface PostProcessingConfig {
  enabled: boolean
  effects: {
    [PostProcessingEffect.BLOOM]?: {
      enabled: boolean
      strength: number
      radius: number
      threshold: number
    }
    [PostProcessingEffect.SSAO]?: {
      enabled: boolean
      intensity: number
      radius: number
      bias: number
    }
    [PostProcessingEffect.FXAA]?: {
      enabled: boolean
    }
    [PostProcessingEffect.TONE_MAPPING]?: {
      enabled: boolean
      exposure: number
      type: THREE.ToneMapping
    }
  }
}

/**
 * 后处理管道
 * 
 * 管理和应用各种后处理效果
 */
export class PostProcessingPipeline {
  private renderer?: THREE.WebGLRenderer
  private renderTarget?: THREE.WebGLRenderTarget
  private finalRenderTarget?: THREE.WebGLRenderTarget
  private config: PostProcessingConfig
  private effects = new Map<PostProcessingEffect, any>()
  private enabled = false

  // 着色器材质
  private copyMaterial?: THREE.ShaderMaterial
  private bloomMaterial?: THREE.ShaderMaterial
  private ssaoMaterial?: THREE.ShaderMaterial
  private fxaaMaterial?: THREE.ShaderMaterial

  // 几何体
  private fullscreenQuad?: THREE.Mesh

  constructor() {
    this.config = {
      enabled: true,
      effects: {
        [PostProcessingEffect.BLOOM]: {
          enabled: false,
          strength: 1.0,
          radius: 0.4,
          threshold: 0.85
        },
        [PostProcessingEffect.SSAO]: {
          enabled: false,
          intensity: 0.5,
          radius: 0.1,
          bias: 0.025
        },
        [PostProcessingEffect.FXAA]: {
          enabled: true
        },
        [PostProcessingEffect.TONE_MAPPING]: {
          enabled: true,
          exposure: 1.0,
          type: THREE.ACESFilmicToneMapping
        }
      }
    }
  }

  /**
   * 初始化后处理管道
   */
  async initialize(renderer: THREE.WebGLRenderer, width: number, height: number): Promise<void> {
    this.renderer = renderer
    
    // 创建渲染目标
    this.createRenderTargets(width, height)
    
    // 创建着色器材质
    this.createShaderMaterials()
    
    // 创建全屏四边形
    this.createFullscreenQuad()
    
    this.enabled = this.config.enabled
    
    console.log('Post-processing pipeline initialized')
  }

  /**
   * 渲染后处理
   */
  render(scene: THREE.Scene, camera: THREE.Camera): void {
    if (!this.enabled || !this.renderer || !this.renderTarget) {
      this.renderer?.render(scene, camera)
      return
    }

    // 渲染到纹理
    this.renderer.setRenderTarget(this.renderTarget)
    this.renderer.render(scene, camera)

    // 应用后处理效果
    let currentTarget = this.renderTarget
    
    // Bloom 效果
    if (this.config.effects.bloom?.enabled) {
      currentTarget = this.applyBloom(currentTarget)
    }

    // SSAO 效果
    if (this.config.effects.ssao?.enabled) {
      currentTarget = this.applySSAO(currentTarget, camera)
    }

    // FXAA 抗锯齿
    if (this.config.effects.fxaa?.enabled) {
      currentTarget = this.applyFXAA(currentTarget)
    }

    // 色调映射
    if (this.config.effects.toneMapping?.enabled) {
      this.applyToneMapping(currentTarget)
    }

    // 渲染到屏幕
    this.renderToScreen(currentTarget)
  }

  /**
   * 设置配置
   */
  setConfig(config: Partial<PostProcessingConfig>): void {
    this.config = { ...this.config, ...config }
    this.enabled = this.config.enabled
  }

  /**
   * 获取配置
   */
  getConfig(): PostProcessingConfig {
    return { ...this.config }
  }

  /**
   * 启用/禁用后处理
   */
  setEnabled(enabled: boolean): void {
    this.enabled = enabled
    this.config.enabled = enabled
  }

  /**
   * 检查是否启用
   */
  isEnabled(): boolean {
    return this.enabled
  }

  /**
   * 调整大小
   */
  resize(width: number, height: number): void {
    if (this.renderTarget) {
      this.renderTarget.setSize(width, height)
    }
    if (this.finalRenderTarget) {
      this.finalRenderTarget.setSize(width, height)
    }
  }

  /**
   * 销毁后处理管道
   */
  async destroy(): Promise<void> {
    // 销毁渲染目标
    this.renderTarget?.dispose()
    this.finalRenderTarget?.dispose()

    // 销毁材质
    this.copyMaterial?.dispose()
    this.bloomMaterial?.dispose()
    this.ssaoMaterial?.dispose()
    this.fxaaMaterial?.dispose()

    // 清理引用
    this.renderer = undefined
    this.renderTarget = undefined
    this.finalRenderTarget = undefined
    this.fullscreenQuad = undefined

    console.log('Post-processing pipeline destroyed')
  }

  /**
   * 创建渲染目标
   */
  private createRenderTargets(width: number, height: number): void {
    const parameters = {
      minFilter: THREE.LinearFilter,
      magFilter: THREE.LinearFilter,
      format: THREE.RGBAFormat,
      type: THREE.FloatType
    }

    this.renderTarget = new THREE.WebGLRenderTarget(width, height, parameters)
    this.finalRenderTarget = new THREE.WebGLRenderTarget(width, height, parameters)
  }

  /**
   * 创建着色器材质
   */
  private createShaderMaterials(): void {
    // 复制材质
    this.copyMaterial = new THREE.ShaderMaterial({
      uniforms: {
        tDiffuse: { value: null },
        opacity: { value: 1.0 }
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform sampler2D tDiffuse;
        uniform float opacity;
        varying vec2 vUv;
        void main() {
          vec4 texel = texture2D(tDiffuse, vUv);
          gl_FragColor = opacity * texel;
        }
      `
    })

    // FXAA 材质
    this.fxaaMaterial = new THREE.ShaderMaterial({
      uniforms: {
        tDiffuse: { value: null },
        resolution: { value: new THREE.Vector2() }
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform sampler2D tDiffuse;
        uniform vec2 resolution;
        varying vec2 vUv;
        
        void main() {
          vec2 texelSize = 1.0 / resolution;
          vec3 color = texture2D(tDiffuse, vUv).rgb;
          
          // 简化的 FXAA 实现
          vec3 colorNW = texture2D(tDiffuse, vUv + vec2(-texelSize.x, -texelSize.y)).rgb;
          vec3 colorNE = texture2D(tDiffuse, vUv + vec2(texelSize.x, -texelSize.y)).rgb;
          vec3 colorSW = texture2D(tDiffuse, vUv + vec2(-texelSize.x, texelSize.y)).rgb;
          vec3 colorSE = texture2D(tDiffuse, vUv + vec2(texelSize.x, texelSize.y)).rgb;
          
          vec3 colorM = (colorNW + colorNE + colorSW + colorSE) * 0.25;
          vec3 luma = vec3(0.299, 0.587, 0.114);
          float lumaNW = dot(colorNW, luma);
          float lumaNE = dot(colorNE, luma);
          float lumaSW = dot(colorSW, luma);
          float lumaSE = dot(colorSE, luma);
          float lumaM = dot(colorM, luma);
          
          float lumaMin = min(lumaM, min(min(lumaNW, lumaNE), min(lumaSW, lumaSE)));
          float lumaMax = max(lumaM, max(max(lumaNW, lumaNE), max(lumaSW, lumaSE)));
          
          if (lumaMax - lumaMin < 0.1) {
            gl_FragColor = vec4(color, 1.0);
          } else {
            gl_FragColor = vec4(colorM, 1.0);
          }
        }
      `
    })
  }

  /**
   * 创建全屏四边形
   */
  private createFullscreenQuad(): void {
    const geometry = new THREE.PlaneGeometry(2, 2)
    this.fullscreenQuad = new THREE.Mesh(geometry, this.copyMaterial)
  }

  /**
   * 应用 Bloom 效果
   */
  private applyBloom(inputTarget: THREE.WebGLRenderTarget): THREE.WebGLRenderTarget {
    // 简化的 Bloom 实现
    return inputTarget
  }

  /**
   * 应用 SSAO 效果
   */
  private applySSAO(inputTarget: THREE.WebGLRenderTarget, camera: THREE.Camera): THREE.WebGLRenderTarget {
    // 简化的 SSAO 实现
    return inputTarget
  }

  /**
   * 应用 FXAA 抗锯齿
   */
  private applyFXAA(inputTarget: THREE.WebGLRenderTarget): THREE.WebGLRenderTarget {
    if (!this.fxaaMaterial || !this.finalRenderTarget || !this.renderer) {
      return inputTarget
    }

    this.fxaaMaterial.uniforms.tDiffuse.value = inputTarget.texture
    this.fxaaMaterial.uniforms.resolution.value.set(inputTarget.width, inputTarget.height)

    this.renderer.setRenderTarget(this.finalRenderTarget)
    this.renderer.render(this.fullscreenQuad!, new THREE.OrthographicCamera())

    return this.finalRenderTarget
  }

  /**
   * 应用色调映射
   */
  private applyToneMapping(inputTarget: THREE.WebGLRenderTarget): void {
    if (!this.renderer) return

    const config = this.config.effects.toneMapping!
    this.renderer.toneMapping = config.type
    this.renderer.toneMappingExposure = config.exposure
  }

  /**
   * 渲染到屏幕
   */
  private renderToScreen(inputTarget: THREE.WebGLRenderTarget): void {
    if (!this.copyMaterial || !this.renderer) return

    this.copyMaterial.uniforms.tDiffuse.value = inputTarget.texture
    this.renderer.setRenderTarget(null)
    this.renderer.render(this.fullscreenQuad!, new THREE.OrthographicCamera())
  }
}
