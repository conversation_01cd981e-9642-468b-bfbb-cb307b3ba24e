/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

/**
 * 资产类型枚举
 */
export enum AssetType {
  MODEL = 'model',
  TEXTURE = 'texture',
  MATERIAL = 'material',
  AUDIO = 'audio',
  VIDEO = 'video',
  SCRIPT = 'script',
  SCENE = 'scene',
  PREFAB = 'prefab'
}

/**
 * 资产项接口
 */
export interface AssetItem {
  id: string
  name: string
  type: AssetType
  path: string
  size: number
  lastModified: Date
  thumbnail?: string
  metadata?: Record<string, any>
}

/**
 * 资产文件夹接口
 */
export interface AssetFolder {
  id: string
  name: string
  path: string
  parent?: string
  children: string[]
  assets: string[]
}

/**
 * 资产浏览器
 * 
 * 管理和浏览项目资产
 */
export class AssetBrowser {
  private assets = new Map<string, AssetItem>()
  private folders = new Map<string, AssetFolder>()
  private currentFolder = 'root'
  private selectedAssets = new Set<string>()
  
  // UI 元素
  private container?: HTMLElement
  private searchInput?: HTMLInputElement
  private filterSelect?: HTMLSelectElement
  private assetGrid?: HTMLElement
  private folderTree?: HTMLElement

  // 过滤和搜索
  private searchQuery = ''
  private typeFilter = AssetType.MODEL
  private sortBy: 'name' | 'type' | 'size' | 'date' = 'name'
  private sortOrder: 'asc' | 'desc' = 'asc'

  /**
   * 初始化资产浏览器
   */
  async initialize(): Promise<void> {
    this.createUI()
    this.createDefaultFolders()
    this.loadAssets()
    console.log('Asset browser initialized')
  }

  /**
   * 更新资产浏览器
   */
  update(deltaTime: number): void {
    // 更新缩略图生成等异步操作
  }

  /**
   * 添加资产
   */
  addAsset(asset: AssetItem): void {
    this.assets.set(asset.id, asset)
    
    // 添加到当前文件夹
    const folder = this.folders.get(this.currentFolder)
    if (folder) {
      folder.assets.push(asset.id)
    }
    
    this.refreshAssetGrid()
  }

  /**
   * 删除资产
   */
  removeAsset(assetId: string): void {
    const asset = this.assets.get(assetId)
    if (!asset) return

    this.assets.delete(assetId)
    
    // 从所有文件夹中移除
    for (const folder of this.folders.values()) {
      const index = folder.assets.indexOf(assetId)
      if (index !== -1) {
        folder.assets.splice(index, 1)
      }
    }
    
    this.refreshAssetGrid()
  }

  /**
   * 获取资产
   */
  getAsset(assetId: string): AssetItem | undefined {
    return this.assets.get(assetId)
  }

  /**
   * 创建文件夹
   */
  createFolder(name: string, parentId?: string): AssetFolder {
    const folder: AssetFolder = {
      id: `folder_${Date.now()}`,
      name,
      path: parentId ? `${this.folders.get(parentId)?.path}/${name}` : name,
      parent: parentId,
      children: [],
      assets: []
    }

    this.folders.set(folder.id, folder)
    
    if (parentId) {
      const parent = this.folders.get(parentId)
      if (parent) {
        parent.children.push(folder.id)
      }
    }
    
    this.refreshFolderTree()
    return folder
  }

  /**
   * 删除文件夹
   */
  deleteFolder(folderId: string): void {
    const folder = this.folders.get(folderId)
    if (!folder || folderId === 'root') return

    // 递归删除子文件夹
    for (const childId of folder.children) {
      this.deleteFolder(childId)
    }
    
    // 删除文件夹中的资产
    for (const assetId of folder.assets) {
      this.assets.delete(assetId)
    }
    
    // 从父文件夹中移除
    if (folder.parent) {
      const parent = this.folders.get(folder.parent)
      if (parent) {
        const index = parent.children.indexOf(folderId)
        if (index !== -1) {
          parent.children.splice(index, 1)
        }
      }
    }
    
    this.folders.delete(folderId)
    this.refreshFolderTree()
  }

  /**
   * 设置当前文件夹
   */
  setCurrentFolder(folderId: string): void {
    if (this.folders.has(folderId)) {
      this.currentFolder = folderId
      this.refreshAssetGrid()
    }
  }

  /**
   * 搜索资产
   */
  searchAssets(query: string): void {
    this.searchQuery = query.toLowerCase()
    this.refreshAssetGrid()
  }

  /**
   * 设置类型过滤
   */
  setTypeFilter(type: AssetType | 'all'): void {
    this.typeFilter = type as AssetType
    this.refreshAssetGrid()
  }

  /**
   * 设置排序
   */
  setSorting(sortBy: 'name' | 'type' | 'size' | 'date', order: 'asc' | 'desc'): void {
    this.sortBy = sortBy
    this.sortOrder = order
    this.refreshAssetGrid()
  }

  /**
   * 选择资产
   */
  selectAsset(assetId: string, addToSelection = false): void {
    if (!addToSelection) {
      this.selectedAssets.clear()
    }
    this.selectedAssets.add(assetId)
    this.updateAssetSelection()
  }

  /**
   * 取消选择资产
   */
  deselectAsset(assetId: string): void {
    this.selectedAssets.delete(assetId)
    this.updateAssetSelection()
  }

  /**
   * 清除选择
   */
  clearSelection(): void {
    this.selectedAssets.clear()
    this.updateAssetSelection()
  }

  /**
   * 导入资产
   */
  async importAssets(files: FileList): Promise<void> {
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      await this.importSingleAsset(file)
    }
  }

  /**
   * 序列化资产数据
   */
  serialize(): any {
    return {
      assets: Array.from(this.assets.values()),
      folders: Array.from(this.folders.values()),
      currentFolder: this.currentFolder
    }
  }

  /**
   * 反序列化资产数据
   */
  deserialize(data: any): void {
    if (data.assets) {
      this.assets.clear()
      for (const asset of data.assets) {
        this.assets.set(asset.id, asset)
      }
    }
    
    if (data.folders) {
      this.folders.clear()
      for (const folder of data.folders) {
        this.folders.set(folder.id, folder)
      }
    }
    
    if (data.currentFolder) {
      this.currentFolder = data.currentFolder
    }
    
    this.refreshFolderTree()
    this.refreshAssetGrid()
  }

  /**
   * 创建 UI
   */
  private createUI(): void {
    this.container = document.createElement('div')
    this.container.className = 'asset-browser'
    this.container.innerHTML = `
      <div class="asset-browser-header">
        <h3>资产浏览器</h3>
        <div class="asset-browser-toolbar">
          <input type="text" class="search-input" placeholder="搜索资产...">
          <select class="filter-select">
            <option value="all">所有类型</option>
            <option value="model">模型</option>
            <option value="texture">纹理</option>
            <option value="material">材质</option>
            <option value="audio">音频</option>
            <option value="video">视频</option>
          </select>
          <button class="import-button">导入</button>
        </div>
      </div>
      <div class="asset-browser-content">
        <div class="folder-tree"></div>
        <div class="asset-grid"></div>
      </div>
    `

    // 获取 UI 元素引用
    this.searchInput = this.container.querySelector('.search-input') as HTMLInputElement
    this.filterSelect = this.container.querySelector('.filter-select') as HTMLSelectElement
    this.assetGrid = this.container.querySelector('.asset-grid') as HTMLElement
    this.folderTree = this.container.querySelector('.folder-tree') as HTMLElement

    // 绑定事件
    this.bindEvents()

    // 添加到 DOM
    const browserContainer = document.getElementById('asset-browser-container')
    if (browserContainer) {
      browserContainer.appendChild(this.container)
    }
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    if (this.searchInput) {
      this.searchInput.addEventListener('input', (e) => {
        this.searchAssets((e.target as HTMLInputElement).value)
      })
    }

    if (this.filterSelect) {
      this.filterSelect.addEventListener('change', (e) => {
        this.setTypeFilter((e.target as HTMLSelectElement).value as AssetType)
      })
    }

    const importButton = this.container?.querySelector('.import-button')
    if (importButton) {
      importButton.addEventListener('click', () => {
        this.showImportDialog()
      })
    }
  }

  /**
   * 创建默认文件夹
   */
  private createDefaultFolders(): void {
    // 根文件夹
    this.folders.set('root', {
      id: 'root',
      name: 'Assets',
      path: '',
      children: [],
      assets: []
    })

    // 默认子文件夹
    const defaultFolders = ['Models', 'Textures', 'Materials', 'Audio', 'Scripts']
    for (const folderName of defaultFolders) {
      this.createFolder(folderName, 'root')
    }
  }

  /**
   * 加载资产
   */
  private async loadAssets(): Promise<void> {
    // 这里应该从服务器或本地存储加载资产
    // 暂时创建一些示例资产
    const sampleAssets: AssetItem[] = [
      {
        id: 'asset_1',
        name: 'cube.glb',
        type: AssetType.MODEL,
        path: '/assets/models/cube.glb',
        size: 1024,
        lastModified: new Date()
      },
      {
        id: 'asset_2',
        name: 'texture.jpg',
        type: AssetType.TEXTURE,
        path: '/assets/textures/texture.jpg',
        size: 2048,
        lastModified: new Date()
      }
    ]

    for (const asset of sampleAssets) {
      this.addAsset(asset)
    }
  }

  /**
   * 刷新文件夹树
   */
  private refreshFolderTree(): void {
    if (!this.folderTree) return

    const html = this.renderFolderTree('root', 0)
    this.folderTree.innerHTML = html
  }

  /**
   * 渲染文件夹树
   */
  private renderFolderTree(folderId: string, depth: number): string {
    const folder = this.folders.get(folderId)
    if (!folder) return ''

    const indent = '  '.repeat(depth)
    const isSelected = folderId === this.currentFolder
    
    let html = `
      <div class="folder-item ${isSelected ? 'selected' : ''}" 
           data-folder-id="${folderId}" 
           style="padding-left: ${depth * 20}px">
        <span class="folder-icon">📁</span>
        <span class="folder-name">${folder.name}</span>
      </div>
    `

    // 渲染子文件夹
    for (const childId of folder.children) {
      html += this.renderFolderTree(childId, depth + 1)
    }

    return html
  }

  /**
   * 刷新资产网格
   */
  private refreshAssetGrid(): void {
    if (!this.assetGrid) return

    const folder = this.folders.get(this.currentFolder)
    if (!folder) return

    const filteredAssets = this.getFilteredAssets(folder.assets)
    const sortedAssets = this.sortAssets(filteredAssets)

    let html = ''
    for (const asset of sortedAssets) {
      const isSelected = this.selectedAssets.has(asset.id)
      html += `
        <div class="asset-item ${isSelected ? 'selected' : ''}" data-asset-id="${asset.id}">
          <div class="asset-thumbnail">
            ${this.getAssetIcon(asset.type)}
          </div>
          <div class="asset-name">${asset.name}</div>
          <div class="asset-info">${this.formatFileSize(asset.size)}</div>
        </div>
      `
    }

    this.assetGrid.innerHTML = html
    this.bindAssetEvents()
  }

  /**
   * 获取过滤后的资产
   */
  private getFilteredAssets(assetIds: string[]): AssetItem[] {
    return assetIds
      .map(id => this.assets.get(id))
      .filter(asset => asset !== undefined) as AssetItem[]
      .filter(asset => {
        // 类型过滤
        if (this.typeFilter !== 'all' && asset.type !== this.typeFilter) {
          return false
        }
        
        // 搜索过滤
        if (this.searchQuery && !asset.name.toLowerCase().includes(this.searchQuery)) {
          return false
        }
        
        return true
      })
  }

  /**
   * 排序资产
   */
  private sortAssets(assets: AssetItem[]): AssetItem[] {
    return assets.sort((a, b) => {
      let comparison = 0
      
      switch (this.sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'type':
          comparison = a.type.localeCompare(b.type)
          break
        case 'size':
          comparison = a.size - b.size
          break
        case 'date':
          comparison = a.lastModified.getTime() - b.lastModified.getTime()
          break
      }
      
      return this.sortOrder === 'asc' ? comparison : -comparison
    })
  }

  /**
   * 绑定资产事件
   */
  private bindAssetEvents(): void {
    if (!this.assetGrid) return

    const assetItems = this.assetGrid.querySelectorAll('.asset-item')
    assetItems.forEach(item => {
      item.addEventListener('click', (e) => {
        const assetId = item.getAttribute('data-asset-id')
        if (assetId) {
          this.selectAsset(assetId, e.ctrlKey || e.metaKey)
        }
      })

      item.addEventListener('dblclick', (e) => {
        const assetId = item.getAttribute('data-asset-id')
        if (assetId) {
          this.openAsset(assetId)
        }
      })
    })
  }

  /**
   * 更新资产选择状态
   */
  private updateAssetSelection(): void {
    if (!this.assetGrid) return

    const assetItems = this.assetGrid.querySelectorAll('.asset-item')
    assetItems.forEach(item => {
      const assetId = item.getAttribute('data-asset-id')
      if (assetId) {
        item.classList.toggle('selected', this.selectedAssets.has(assetId))
      }
    })
  }

  /**
   * 获取资产图标
   */
  private getAssetIcon(type: AssetType): string {
    const icons = {
      [AssetType.MODEL]: '🎲',
      [AssetType.TEXTURE]: '🖼️',
      [AssetType.MATERIAL]: '🎨',
      [AssetType.AUDIO]: '🔊',
      [AssetType.VIDEO]: '🎬',
      [AssetType.SCRIPT]: '📜',
      [AssetType.SCENE]: '🌍',
      [AssetType.PREFAB]: '🧩'
    }
    return icons[type] || '📄'
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 显示导入对话框
   */
  private showImportDialog(): void {
    const input = document.createElement('input')
    input.type = 'file'
    input.multiple = true
    input.accept = '.glb,.gltf,.jpg,.jpeg,.png,.mp3,.wav,.mp4'
    
    input.onchange = (e) => {
      const files = (e.target as HTMLInputElement).files
      if (files) {
        this.importAssets(files)
      }
    }
    
    input.click()
  }

  /**
   * 导入单个资产
   */
  private async importSingleAsset(file: File): Promise<void> {
    const asset: AssetItem = {
      id: `asset_${Date.now()}_${Math.random()}`,
      name: file.name,
      type: this.getAssetTypeFromFile(file),
      path: `/assets/${file.name}`,
      size: file.size,
      lastModified: new Date(file.lastModified)
    }

    // 这里应该上传文件到服务器
    // 暂时直接添加到资产列表
    this.addAsset(asset)
  }

  /**
   * 根据文件获取资产类型
   */
  private getAssetTypeFromFile(file: File): AssetType {
    const ext = file.name.split('.').pop()?.toLowerCase()
    
    switch (ext) {
      case 'glb':
      case 'gltf':
      case 'fbx':
      case 'obj':
        return AssetType.MODEL
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'webp':
        return AssetType.TEXTURE
      case 'mp3':
      case 'wav':
      case 'ogg':
        return AssetType.AUDIO
      case 'mp4':
      case 'webm':
        return AssetType.VIDEO
      case 'js':
      case 'ts':
        return AssetType.SCRIPT
      default:
        return AssetType.MODEL
    }
  }

  /**
   * 打开资产
   */
  private openAsset(assetId: string): void {
    const asset = this.assets.get(assetId)
    if (!asset) return

    // 根据资产类型执行相应的打开操作
    switch (asset.type) {
      case AssetType.MODEL:
        // 加载模型到场景
        break
      case AssetType.TEXTURE:
        // 在纹理查看器中打开
        break
      case AssetType.SCRIPT:
        // 在代码编辑器中打开
        break
    }
  }

  /**
   * 销毁资产浏览器
   */
  async destroy(): Promise<void> {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container)
    }
    
    this.assets.clear()
    this.folders.clear()
    this.selectedAssets.clear()

    console.log('Asset browser destroyed')
  }
}
