/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import { EngineConfig, mergeConfig, validateConfig } from './config'
import { RenderingSystem } from './rendering'
import { AssetManager } from './assets'
import { AnimationSystem } from './animation'
import { SceneManager } from './scene'
import { AudioSystem } from './audio'
import { ECSWorld } from '../ecs'
import { PhysicsWorld } from '../physics'
import { StateManager } from '../state'
import { XRManager } from '../xr'

/**
 * DL-Engine 核心引擎类
 * 
 * 负责初始化和管理所有子系统
 */
export class Engine {
  private config: EngineConfig
  private isInitialized = false
  private isRunning = false
  private animationFrameId?: number

  // 核心系统
  public readonly rendering: RenderingSystem
  public readonly assets: AssetManager
  public readonly animation: AnimationSystem
  public readonly scene: SceneManager
  public readonly audio: AudioSystem
  public readonly ecs: ECSWorld
  public readonly physics: PhysicsWorld
  public readonly state: StateManager
  public readonly xr: XRManager

  // 性能监控
  private lastTime = 0
  private frameCount = 0
  private fps = 0

  constructor(userConfig: Partial<EngineConfig> = {}) {
    this.config = mergeConfig(userConfig)
    
    // 验证配置
    const errors = validateConfig(this.config)
    if (errors.length > 0) {
      throw new Error(`Engine configuration errors: ${errors.join(', ')}`)
    }

    // 初始化核心系统
    this.rendering = new RenderingSystem(this.config.rendering)
    this.assets = new AssetManager()
    this.animation = new AnimationSystem()
    this.scene = new SceneManager()
    this.audio = new AudioSystem(this.config.audio)
    this.ecs = new ECSWorld()
    this.physics = new PhysicsWorld(this.config.physics)
    this.state = new StateManager()
    this.xr = new XRManager(this.config.xr)

    console.log('DL-Engine initialized with config:', this.config)
  }

  /**
   * 初始化引擎
   */
  async initialize(canvas?: HTMLCanvasElement): Promise<void> {
    if (this.isInitialized) {
      console.warn('Engine is already initialized')
      return
    }

    try {
      // 初始化渲染系统
      await this.rendering.initialize(canvas)

      // 初始化音频系统
      if (this.config.audio.enabled) {
        await this.audio.initialize()
      }

      // 初始化物理系统
      await this.physics.initialize()

      // 初始化 XR 系统
      if (this.config.xr.enabled) {
        await this.xr.initialize()
      }

      // 初始化资产管理器
      await this.assets.initialize()

      // 设置系统间的依赖关系
      this.setupSystemDependencies()

      this.isInitialized = true
      console.log('DL-Engine initialized successfully')
    } catch (error) {
      console.error('Failed to initialize DL-Engine:', error)
      throw error
    }
  }

  /**
   * 启动引擎
   */
  start(): void {
    if (!this.isInitialized) {
      throw new Error('Engine must be initialized before starting')
    }

    if (this.isRunning) {
      console.warn('Engine is already running')
      return
    }

    this.isRunning = true
    this.lastTime = performance.now()
    this.tick()
    console.log('DL-Engine started')
  }

  /**
   * 停止引擎
   */
  stop(): void {
    if (!this.isRunning) {
      return
    }

    this.isRunning = false
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId)
      this.animationFrameId = undefined
    }
    console.log('DL-Engine stopped')
  }

  /**
   * 销毁引擎
   */
  async destroy(): Promise<void> {
    this.stop()

    // 销毁所有系统
    await this.xr.destroy()
    await this.physics.destroy()
    await this.audio.destroy()
    await this.assets.destroy()
    await this.rendering.destroy()

    this.isInitialized = false
    console.log('DL-Engine destroyed')
  }

  /**
   * 主循环
   */
  private tick = (): void => {
    if (!this.isRunning) return

    const currentTime = performance.now()
    const deltaTime = (currentTime - this.lastTime) / 1000
    this.lastTime = currentTime

    // 更新 FPS
    this.frameCount++
    if (this.frameCount % 60 === 0) {
      this.fps = Math.round(1 / deltaTime)
    }

    try {
      // 更新所有系统
      this.update(deltaTime)
      
      // 渲染
      this.render()
    } catch (error) {
      console.error('Error in engine tick:', error)
    }

    this.animationFrameId = requestAnimationFrame(this.tick)
  }

  /**
   * 更新所有系统
   */
  private update(deltaTime: number): void {
    // 更新物理系统
    this.physics.update(deltaTime)

    // 更新 ECS 系统
    this.ecs.update(deltaTime)

    // 更新动画系统
    this.animation.update(deltaTime)

    // 更新音频系统
    this.audio.update(deltaTime)

    // 更新 XR 系统
    this.xr.update(deltaTime)

    // 更新状态管理器
    this.state.update(deltaTime)
  }

  /**
   * 渲染
   */
  private render(): void {
    this.rendering.render(this.scene.getActiveScene())
  }

  /**
   * 设置系统间的依赖关系
   */
  private setupSystemDependencies(): void {
    // 将渲染器传递给场景管理器
    this.scene.setRenderer(this.rendering.getRenderer())

    // 将场景传递给物理系统
    this.physics.setScene(this.scene.getActiveScene())

    // 设置其他依赖关系...
  }

  /**
   * 获取当前 FPS
   */
  getFPS(): number {
    return this.fps
  }

  /**
   * 获取引擎配置
   */
  getConfig(): EngineConfig {
    return { ...this.config }
  }

  /**
   * 检查引擎是否已初始化
   */
  isEngineInitialized(): boolean {
    return this.isInitialized
  }

  /**
   * 检查引擎是否正在运行
   */
  isEngineRunning(): boolean {
    return this.isRunning
  }
}
