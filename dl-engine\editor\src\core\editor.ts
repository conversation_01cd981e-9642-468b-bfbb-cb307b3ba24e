/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import { Engine } from '../../engine/src'
import { SceneEditor } from './scene-editor'
import { PropertiesPanel } from './properties'
import { AssetBrowser } from './assets'
import { HierarchyPanel } from './hierarchy'
import { EditorToolbar } from './toolbar'

/**
 * 编辑器状态枚举
 */
export enum EditorState {
  IDLE = 'idle',
  EDITING = 'editing',
  PLAYING = 'playing',
  PAUSED = 'paused'
}

/**
 * 编辑器配置接口
 */
export interface EditorConfig {
  autoSave: boolean
  autoSaveInterval: number
  showGrid: boolean
  showGizmos: boolean
  snapToGrid: boolean
  gridSize: number
  language: 'zh-CN' | 'en-US'
  theme: 'light' | 'dark'
}

/**
 * 编辑器核心类
 * 
 * 管理整个编辑器的状态和各个子系统
 */
export class EditorCore {
  private engine?: Engine
  private sceneEditor: SceneEditor
  private propertiesPanel: PropertiesPanel
  private assetBrowser: AssetBrowser
  private hierarchyPanel: HierarchyPanel
  private toolbar: EditorToolbar

  private state = EditorState.IDLE
  private config: EditorConfig
  private isInitialized = false

  // 选择系统
  private selectedObjects = new Set<string>()
  private clipboard: any[] = []

  // 历史系统
  private history: any[] = []
  private historyIndex = -1
  private maxHistorySize = 100

  constructor() {
    this.config = {
      autoSave: true,
      autoSaveInterval: 30000, // 30秒
      showGrid: true,
      showGizmos: true,
      snapToGrid: false,
      gridSize: 1,
      language: 'zh-CN',
      theme: 'light'
    }

    // 初始化子系统
    this.sceneEditor = new SceneEditor()
    this.propertiesPanel = new PropertiesPanel()
    this.assetBrowser = new AssetBrowser()
    this.hierarchyPanel = new HierarchyPanel()
    this.toolbar = new EditorToolbar()
  }

  /**
   * 初始化编辑器
   */
  async initialize(canvas: HTMLCanvasElement): Promise<void> {
    if (this.isInitialized) {
      console.warn('Editor is already initialized')
      return
    }

    try {
      // 初始化引擎
      this.engine = new Engine({
        rendering: {
          renderer: 'webgl',
          antialias: true,
          shadows: true,
          postprocessing: true,
          targetFPS: 60,
          adaptiveResolution: true,
          maxPixelRatio: 2
        },
        debug: true,
        performance: true
      })

      await this.engine.initialize(canvas)

      // 初始化子系统
      await this.sceneEditor.initialize(this.engine)
      await this.propertiesPanel.initialize()
      await this.assetBrowser.initialize()
      await this.hierarchyPanel.initialize()
      await this.toolbar.initialize()

      // 设置子系统之间的依赖关系
      this.setupDependencies()

      // 启动引擎
      this.engine.start()

      this.isInitialized = true
      console.log('Editor initialized successfully')
    } catch (error) {
      console.error('Failed to initialize editor:', error)
      throw error
    }
  }

  /**
   * 更新编辑器
   */
  update(deltaTime: number): void {
    if (!this.isInitialized || !this.engine) {
      return
    }

    // 更新子系统
    this.sceneEditor.update(deltaTime)
    this.propertiesPanel.update(deltaTime)
    this.assetBrowser.update(deltaTime)
    this.hierarchyPanel.update(deltaTime)
    this.toolbar.update(deltaTime)
  }

  /**
   * 设置编辑器状态
   */
  setState(state: EditorState): void {
    const oldState = this.state
    this.state = state

    // 通知子系统状态变化
    this.sceneEditor.onStateChange(oldState, state)
    this.toolbar.onStateChange(oldState, state)

    console.log(`Editor state changed: ${oldState} -> ${state}`)
  }

  /**
   * 获取编辑器状态
   */
  getState(): EditorState {
    return this.state
  }

  /**
   * 播放场景
   */
  play(): void {
    if (this.state === EditorState.EDITING || this.state === EditorState.IDLE) {
      this.setState(EditorState.PLAYING)
    }
  }

  /**
   * 暂停场景
   */
  pause(): void {
    if (this.state === EditorState.PLAYING) {
      this.setState(EditorState.PAUSED)
    }
  }

  /**
   * 停止场景
   */
  stop(): void {
    if (this.state === EditorState.PLAYING || this.state === EditorState.PAUSED) {
      this.setState(EditorState.EDITING)
    }
  }

  /**
   * 选择对象
   */
  selectObject(objectId: string, addToSelection = false): void {
    if (!addToSelection) {
      this.selectedObjects.clear()
    }
    this.selectedObjects.add(objectId)
    
    // 通知属性面板更新
    this.propertiesPanel.setSelectedObjects(Array.from(this.selectedObjects))
  }

  /**
   * 取消选择对象
   */
  deselectObject(objectId: string): void {
    this.selectedObjects.delete(objectId)
    this.propertiesPanel.setSelectedObjects(Array.from(this.selectedObjects))
  }

  /**
   * 清除所有选择
   */
  clearSelection(): void {
    this.selectedObjects.clear()
    this.propertiesPanel.setSelectedObjects([])
  }

  /**
   * 获取选中的对象
   */
  getSelectedObjects(): string[] {
    return Array.from(this.selectedObjects)
  }

  /**
   * 撤销操作
   */
  undo(): void {
    if (this.historyIndex > 0) {
      this.historyIndex--
      const action = this.history[this.historyIndex]
      this.executeAction(action, true)
    }
  }

  /**
   * 重做操作
   */
  redo(): void {
    if (this.historyIndex < this.history.length - 1) {
      this.historyIndex++
      const action = this.history[this.historyIndex]
      this.executeAction(action, false)
    }
  }

  /**
   * 添加历史记录
   */
  addToHistory(action: any): void {
    // 移除当前位置之后的历史记录
    this.history = this.history.slice(0, this.historyIndex + 1)
    
    // 添加新的动作
    this.history.push(action)
    this.historyIndex++
    
    // 限制历史记录大小
    if (this.history.length > this.maxHistorySize) {
      this.history.shift()
      this.historyIndex--
    }
  }

  /**
   * 保存项目
   */
  async saveProject(): Promise<void> {
    try {
      const projectData = this.serializeProject()
      // 这里应该调用实际的保存逻辑
      console.log('Project saved:', projectData)
    } catch (error) {
      console.error('Failed to save project:', error)
      throw error
    }
  }

  /**
   * 加载项目
   */
  async loadProject(projectData: any): Promise<void> {
    try {
      this.deserializeProject(projectData)
      console.log('Project loaded successfully')
    } catch (error) {
      console.error('Failed to load project:', error)
      throw error
    }
  }

  /**
   * 获取引擎实例
   */
  getEngine(): Engine | undefined {
    return this.engine
  }

  /**
   * 获取场景编辑器
   */
  getSceneEditor(): SceneEditor {
    return this.sceneEditor
  }

  /**
   * 获取属性面板
   */
  getPropertiesPanel(): PropertiesPanel {
    return this.propertiesPanel
  }

  /**
   * 获取资产浏览器
   */
  getAssetBrowser(): AssetBrowser {
    return this.assetBrowser
  }

  /**
   * 获取层次面板
   */
  getHierarchyPanel(): HierarchyPanel {
    return this.hierarchyPanel
  }

  /**
   * 获取工具栏
   */
  getToolbar(): EditorToolbar {
    return this.toolbar
  }

  /**
   * 设置子系统之间的依赖关系
   */
  private setupDependencies(): void {
    // 设置场景编辑器的引用
    this.hierarchyPanel.setSceneEditor(this.sceneEditor)
    this.propertiesPanel.setSceneEditor(this.sceneEditor)
    
    // 设置选择回调
    this.sceneEditor.onObjectSelected = (objectId: string, addToSelection: boolean) => {
      this.selectObject(objectId, addToSelection)
    }
    
    this.hierarchyPanel.onObjectSelected = (objectId: string, addToSelection: boolean) => {
      this.selectObject(objectId, addToSelection)
    }
  }

  /**
   * 执行动作
   */
  private executeAction(action: any, isUndo: boolean): void {
    // 这里应该实现具体的动作执行逻辑
    console.log('Executing action:', action, 'isUndo:', isUndo)
  }

  /**
   * 序列化项目
   */
  private serializeProject(): any {
    return {
      version: '1.0.0',
      scene: this.sceneEditor.serialize(),
      assets: this.assetBrowser.serialize(),
      config: this.config
    }
  }

  /**
   * 反序列化项目
   */
  private deserializeProject(data: any): void {
    if (data.scene) {
      this.sceneEditor.deserialize(data.scene)
    }
    if (data.assets) {
      this.assetBrowser.deserialize(data.assets)
    }
    if (data.config) {
      this.config = { ...this.config, ...data.config }
    }
  }

  /**
   * 销毁编辑器
   */
  async destroy(): Promise<void> {
    // 销毁子系统
    await this.toolbar.destroy()
    await this.hierarchyPanel.destroy()
    await this.assetBrowser.destroy()
    await this.propertiesPanel.destroy()
    await this.sceneEditor.destroy()

    // 销毁引擎
    if (this.engine) {
      await this.engine.destroy()
      this.engine = undefined
    }

    this.isInitialized = false
    console.log('Editor destroyed')
  }
}
