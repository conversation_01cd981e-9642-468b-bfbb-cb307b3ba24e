/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

/**
 * DL-Engine 物理引擎
 *
 * 提供物理模拟功能：
 * - Cannon-es 客户端物理
 * - Rapier3D 服务端物理
 * - 碰撞检测
 * - 约束系统
 * - 空间查询
 * - 网络物理同步
 */

// 物理引擎导出
export * from './world'
export * from './cannon'
export * from './rapier'
export * from './collision'
export * from './constraints'
export * from './queries'
export * from './networking'

// 主要类导出
export { PhysicsWorld } from './world'
export { CannonPhysics } from './cannon'
export { RapierPhysics } from './rapier'
export { CollisionDetection } from './collision'
export { ConstraintSystem } from './constraints'
export { SpatialQueries } from './queries'
export { PhysicsNetworking } from './networking'
