/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import * as THREE from 'three'
import { EntityManager } from './entities'

/**
 * 基础组件接口
 */
export interface Component {
  type: string
  entityId: number
  data: any
}

/**
 * 变换组件
 */
export interface TransformComponent extends Component {
  type: 'transform'
  data: {
    position: THREE.Vector3
    rotation: THREE.Quaternion
    scale: THREE.Vector3
    matrix: THREE.Matrix4
  }
}

/**
 * 网格组件
 */
export interface MeshComponent extends Component {
  type: 'mesh'
  data: {
    geometry: THREE.BufferGeometry
    material: THREE.Material | THREE.Material[]
    mesh: THREE.Mesh
  }
}

/**
 * 物理组件
 */
export interface PhysicsComponent extends Component {
  type: 'physics'
  data: {
    body: any // 物理体引用
    type: 'static' | 'dynamic' | 'kinematic'
    mass: number
    friction: number
    restitution: number
  }
}

/**
 * 音频组件
 */
export interface AudioComponent extends Component {
  type: 'audio'
  data: {
    audio: THREE.Audio
    volume: number
    loop: boolean
    autoplay: boolean
  }
}

/**
 * 教育专用组件
 */
export interface EducationComponent extends Component {
  type: 'education'
  data: {
    category: string
    description: string
    interactive: boolean
    metadata: Record<string, any>
  }
}

/**
 * 组件类型联合
 */
export type ComponentTypes = 
  | TransformComponent
  | MeshComponent
  | PhysicsComponent
  | AudioComponent
  | EducationComponent

/**
 * 组件管理器
 * 
 * 负责组件的存储、查询和管理
 */
export class ComponentManager {
  private components = new Map<string, Map<number, Component>>()
  private entityComponents = new Map<number, Set<string>>()
  private entityManager?: EntityManager

  /**
   * 初始化组件管理器
   */
  async initialize(): Promise<void> {
    // 注册默认组件类型
    this.registerComponentType('transform')
    this.registerComponentType('mesh')
    this.registerComponentType('physics')
    this.registerComponentType('audio')
    this.registerComponentType('education')
    
    console.log('Component manager initialized')
  }

  /**
   * 设置实体管理器引用
   */
  setEntityManager(entityManager: EntityManager): void {
    this.entityManager = entityManager
  }

  /**
   * 注册组件类型
   */
  registerComponentType(type: string): void {
    if (!this.components.has(type)) {
      this.components.set(type, new Map())
    }
  }

  /**
   * 添加组件
   */
  addComponent<T>(entityId: number, componentType: string, data: T): void {
    // 检查实体是否存在
    if (this.entityManager && !this.entityManager.isEntityActive(entityId)) {
      throw new Error(`Entity ${entityId} does not exist or is not active`)
    }

    // 确保组件类型已注册
    if (!this.components.has(componentType)) {
      this.registerComponentType(componentType)
    }

    const component: Component = {
      type: componentType,
      entityId,
      data
    }

    // 存储组件
    this.components.get(componentType)!.set(entityId, component)

    // 更新实体组件映射
    if (!this.entityComponents.has(entityId)) {
      this.entityComponents.set(entityId, new Set())
    }
    this.entityComponents.get(entityId)!.add(componentType)
  }

  /**
   * 获取组件
   */
  getComponent<T>(entityId: number, componentType: string): T | undefined {
    const componentMap = this.components.get(componentType)
    if (!componentMap) {
      return undefined
    }

    const component = componentMap.get(entityId)
    return component ? component.data as T : undefined
  }

  /**
   * 移除组件
   */
  removeComponent(entityId: number, componentType: string): boolean {
    const componentMap = this.components.get(componentType)
    if (!componentMap) {
      return false
    }

    const removed = componentMap.delete(entityId)
    if (removed) {
      const entityComponentSet = this.entityComponents.get(entityId)
      if (entityComponentSet) {
        entityComponentSet.delete(componentType)
        if (entityComponentSet.size === 0) {
          this.entityComponents.delete(entityId)
        }
      }
    }

    return removed
  }

  /**
   * 检查实体是否有组件
   */
  hasComponent(entityId: number, componentType: string): boolean {
    const componentMap = this.components.get(componentType)
    return componentMap ? componentMap.has(entityId) : false
  }

  /**
   * 获取实体的所有组件类型
   */
  getEntityComponents(entityId: number): string[] {
    const componentSet = this.entityComponents.get(entityId)
    return componentSet ? Array.from(componentSet) : []
  }

  /**
   * 移除实体的所有组件
   */
  removeAllComponents(entityId: number): void {
    const componentTypes = this.getEntityComponents(entityId)
    for (const componentType of componentTypes) {
      this.removeComponent(entityId, componentType)
    }
  }

  /**
   * 查询拥有指定组件的实体
   */
  queryEntities(componentTypes: string[]): number[] {
    if (componentTypes.length === 0) {
      return []
    }

    // 获取第一个组件类型的所有实体
    const firstComponentMap = this.components.get(componentTypes[0])
    if (!firstComponentMap) {
      return []
    }

    const candidates = Array.from(firstComponentMap.keys())
    
    // 过滤出拥有所有指定组件的实体
    return candidates.filter(entityId => {
      return componentTypes.every(componentType => 
        this.hasComponent(entityId, componentType)
      )
    })
  }

  /**
   * 获取指定类型的所有组件
   */
  getComponentsOfType<T>(componentType: string): Map<number, T> {
    const componentMap = this.components.get(componentType)
    if (!componentMap) {
      return new Map()
    }

    const result = new Map<number, T>()
    for (const [entityId, component] of componentMap) {
      result.set(entityId, component.data as T)
    }
    return result
  }

  /**
   * 获取组件数量
   */
  getComponentCount(): number {
    let count = 0
    for (const componentMap of this.components.values()) {
      count += componentMap.size
    }
    return count
  }

  /**
   * 获取组件类型数量
   */
  getComponentTypeCount(): number {
    return this.components.size
  }

  /**
   * 获取所有注册的组件类型
   */
  getRegisteredComponentTypes(): string[] {
    return Array.from(this.components.keys())
  }

  /**
   * 销毁组件管理器
   */
  async destroy(): Promise<void> {
    this.components.clear()
    this.entityComponents.clear()
    this.entityManager = undefined
    
    console.log('Component manager destroyed')
  }
}
