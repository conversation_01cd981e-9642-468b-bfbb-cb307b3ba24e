## Digital Learning Engine (DL-Engine) 项目重构方案（2025-08-09）

### 执行摘要
- 目标：将现有项目重构为分布式、可观测、可扩展的数字学习引擎，支持教育场景下的3D/VR/AR应用创作与运行。
- 核心要求：手机号登录（默认区号+86）、界面中英双语（默认中文）、ECS 架构、网络化物理与XR、节点式编程、容器化与Kubernetes（Helm/Agones）、边缘计算、多平台（Web/iOS/Android/桌面）。
- 三大目录：dl-engine/engine、dl-engine/editor、dl-engine/server（均使用 npm 管理依赖；engine 与 editor 各自有 package.json；server 下各服务有独立 package.json）。
- 分批次重构：每批约2万行代码，预计约21批（可并行），每批可独立交付与回滚。

### 技术栈与关键版本
- 前端：React 18.2.0 + TypeScript 5.6.3 + Vite 5.4.8 + Redux + Ant Design 5.x + Three.js 0.176.0 + cannon-es + WebXR
- 引擎：ECS（bitECS/自研组合）、网络同步、可插拔渲染与物理、XR 交互
- 后端：Node.js 22 + Nest.js + FeathersJS（实时）+ MySQL（主库）+ Redis（缓存）+ PostgreSQL（向量库）+ Primus WebSocket + MediaSoup（WebRTC）+ Rapier3D 0.11.2（确定性物理/服务端权威）+ Ollama（嵌入/推理）+ Minio（对象存储）
- 部署：Docker/Compose（本地）、Kubernetes + Helm、Agones（游戏/实例服编排）、K8s 原生服务发现与多实例负载均衡、边缘节点（就近接入）

### 顶层目录结构（目标形态）
## DL-Engine 完整目录结构设计

```
dl-engine/                                    # 项目根目录 (414,232行代码重构)
├── engine/                                   # 底层引擎 (120,000行)
│   ├── core/                                # 核心引擎 (45,000行)
│   │   ├── rendering/                       # 渲染系统 (15,000行)
│   │   │   ├── three-integration/           # Three.js集成
│   │   │   ├── materials/                   # 材质系统
│   │   │   ├── lighting/                    # 光照系统
│   │   │   ├── shadows/                     # 阴影系统
│   │   │   └── postprocessing/              # 后处理效果
│   │   ├── assets/                          # 资产系统 (8,000行)
│   │   │   ├── loaders/                     # 加载器 (GLTF/纹理/音频)
│   │   │   ├── cache/                       # 缓存管理
│   │   │   └── streaming/                   # 流式加载
│   │   ├── animation/                       # 动画系统 (7,000行)
│   │   │   ├── skeletal/                    # 骨骼动画
│   │   │   ├── morph/                       # 变形动画
│   │   │   └── timeline/                    # 时间轴
│   │   ├── scene/                           # 场景管理 (5,000行)
│   │   │   ├── graph/                       # 场景图
│   │   │   ├── culling/                     # 视锥剔除
│   │   │   └── lod/                         # 细节层次
│   │   ├── audio/                           # 音频系统 (6,000行)
│   │   │   ├── spatial/                     # 空间音频
│   │   │   ├── streaming/                   # 音频流
│   │   │   └── effects/                     # 音效处理
│   │   └── utils/                           # 工具库 (4,000行)
│   ├── ecs/                                 # ECS系统 (25,000行)
│   │   ├── entities/                        # 实体管理 (8,000行)
│   │   ├── components/                      # 组件系统 (7,000行)
│   │   │   ├── transform/                   # 变换组件
│   │   │   ├── mesh/                        # 网格组件
│   │   │   ├── physics/                     # 物理组件
│   │   │   ├── audio/                       # 音频组件
│   │   │   └── education/                   # 教育专用组件
│   │   ├── systems/                         # 系统调度 (6,000行)
│   │   │   ├── input/                       # 输入系统
│   │   │   ├── simulation/                  # 模拟系统
│   │   │   ├── animation/                   # 动画系统
│   │   │   └── presentation/                # 表现系统
│   │   └── networking/                      # 网络同步 (4,000行)
│   ├── physics/                             # 物理引擎 (30,000行)
│   │   ├── rapier/                          # Rapier3D集成 (12,000行)
│   │   ├── collision/                       # 碰撞检测 (8,000行)
│   │   ├── constraints/                     # 约束系统 (4,000行)
│   │   ├── queries/                         # 空间查询 (4,000行)
│   │   └── networking/                      # 网络物理 (2,000行)
│   ├── state/                               # 状态管理 (20,000行)
│   │   ├── store/                           # 状态存储 (8,000行)
│   │   ├── actions/                         # 动作系统 (6,000行)
│   │   ├── sync/                            # 网络同步 (4,000行)
│   │   └── persistence/                     # 持久化 (2,000行)
│   ├── xr/                                  # XR支持 (15,000行)
│   │   ├── webxr/                           # WebXR集成 (6,000行)
│   │   ├── ui/                              # 3D UI渲染 (5,000行)
│   │   └── interaction/                     # 交互系统 (4,000行)
│   └── ai/                                  # AI集成 (5,000行)
│       ├── ollama/                          # Ollama集成
│       ├── nlp/                             # 自然语言处理
│       └── analytics/                       # 学习分析
├── editor/                                  # 在线编辑器 (85,000行)
│   ├── core/                                # 编辑器核心 (35,000行)
│   │   ├── scene-editor/                    # 场景编辑器 (12,000行)
│   │   │   ├── viewport/                    # 3D视口
│   │   │   ├── gizmos/                      # 操作工具
│   │   │   ├── selection/                   # 选择系统
│   │   │   └── camera/                      # 相机控制
│   │   ├── properties/                      # 属性面板 (8,000行)
│   │   │   ├── inspector/                   # 检查器
│   │   │   ├── materials/                   # 材质编辑
│   │   │   └── components/                  # 组件编辑
│   │   ├── assets/                          # 资产浏览器 (6,000行)
│   │   │   ├── browser/                     # 文件浏览
│   │   │   ├── preview/                     # 预览系统
│   │   │   └── import/                      # 导入工具
│   │   ├── hierarchy/                       # 层次结构 (5,000行)
│   │   │   ├── tree/                        # 场景树
│   │   │   ├── search/                      # 搜索过滤
│   │   │   └── operations/                  # 操作命令
│   │   └── toolbar/                         # 工具栏 (4,000行)
│   ├── ui/                                  # 编辑器界面 (25,000行)
│   │   ├── components/                      # UI组件 (10,000行)
│   │   │   ├── panels/                      # 面板组件
│   │   │   ├── dialogs/                     # 对话框
│   │   │   ├── menus/                       # 菜单系统
│   │   │   └── forms/                       # 表单组件
│   │   ├── layout/                          # 布局系统 (8,000行)
│   │   │   ├── docking/                     # 停靠面板
│   │   │   ├── tabs/                        # 标签页
│   │   │   └── splitters/                   # 分割器
│   │   ├── themes/                          # 主题系统 (4,000行)
│   │   │   ├── light/                       # 浅色主题
│   │   │   ├── dark/                        # 深色主题
│   │   │   └── education/                   # 教育主题
│   │   └── i18n/                            # 国际化 (3,000行)
│   │       ├── zh-CN/                       # 中文语言包
│   │       ├── en-US/                       # 英文语言包
│   │       └── localization/                # 本地化工具
│   ├── visual-script/                       # 可视化脚本 (18,000行)
│   │   ├── editor/                          # 节点编辑器 (8,000行)
│   │   │   ├── canvas/                      # 画布系统
│   │   │   ├── nodes/                       # 节点渲染
│   │   │   ├── connections/                 # 连接系统
│   │   │   └── minimap/                     # 小地图
│   │   ├── engine/                          # 脚本引擎 (6,000行)
│   │   │   ├── execution/                   # 执行引擎
│   │   │   ├── debugging/                   # 调试系统
│   │   │   └── profiling/                   # 性能分析
│   │   └── library/                         # 节点库 (4,000行)
│   │       ├── basic/                       # 基础节点
│   │       ├── math/                        # 数学节点
│   │       ├── logic/                       # 逻辑节点
│   │       └── education/                   # 教育节点
│   └── plugins/                             # 插件系统 (7,000行)
│       ├── api/                             # 插件API
│       ├── loader/                          # 插件加载器
│       └── marketplace/                     # 插件市场
├── server/                                  # 服务器端 (180,000行)
│   ├── gateway/                             # API网关 (15,000行)
│   │   ├── routing/                         # 路由系统
│   │   ├── middleware/                      # 中间件
│   │   ├── rate-limiting/                   # 限流控制
│   │   └── load-balancing/                  # 负载均衡
│   ├── auth/                                # 认证服务 (20,000行)
│   │   ├── phone/                           # 手机号登录 (8,000行)
│   │   │   ├── sms/                         # 短信验证
│   │   │   ├── verification/                # 验证码
│   │   │   └── registration/                # 注册流程
│   │   ├── jwt/                             # JWT令牌 (6,000行)
│   │   ├── oauth/                           # OAuth集成 (4,000行)
│   │   └── permissions/                     # 权限管理 (2,000行)
│   ├── api/                                 # API服务 (65,000行)
│   │   ├── users/                           # 用户管理 (12,000行)
│   │   ├── projects/                        # 项目管理 (10,000行)
│   │   ├── scenes/                          # 场景管理 (8,000行)
│   │   ├── assets/                          # 资产管理 (8,000行)
│   │   ├── social/                          # 社交功能 (6,000行)
│   │   ├── education/                       # 教育功能 (8,000行)
│   │   │   ├── courses/                     # 课程管理
│   │   │   ├── assignments/                 # 作业系统
│   │   │   ├── assessments/                 # 评估系统
│   │   │   └── analytics/                   # 学习分析
│   │   ├── collaboration/                   # 协作功能 (5,000行)
│   │   ├── notifications/                   # 通知系统 (4,000行)
│   │   └── monitoring/                      # 监控接口 (4,000行)
│   ├── instance/                            # 实例服务 (25,000行)
│   │   ├── world/                           # 世界实例 (10,000行)
│   │   ├── networking/                      # 网络同步 (8,000行)
│   │   ├── physics/                         # 物理同步 (4,000行)
│   │   └── scaling/                         # 扩缩容 (3,000行)
│   ├── media/                               # 媒体服务 (20,000行)
│   │   ├── upload/                          # 文件上传 (6,000行)
│   │   ├── processing/                      # 媒体处理 (8,000行)
│   │   │   ├── image/                       # 图像处理
│   │   │   ├── video/                       # 视频处理
│   │   │   ├── audio/                       # 音频处理
│   │   │   └── 3d-models/                   # 3D模型处理
│   │   ├── streaming/                       # 流媒体 (4,000行)
│   │   └── cdn/                             # CDN集成 (2,000行)
│   ├── storage/                             # 存储服务 (15,000行)
│   │   ├── minio/                           # Minio对象存储 (6,000行)
│   │   ├── database/                        # 数据库服务 (5,000行)
│   │   │   ├── mysql/                       # MySQL主库
│   │   │   ├── redis/                       # Redis缓存
│   │   │   └── postgresql/                  # PostgreSQL向量库
│   │   ├── backup/                          # 备份系统 (2,000行)
│   │   └── migration/                       # 数据迁移 (2,000行)
│   ├── ai/                                  # AI服务 (12,000行)
│   │   ├── ollama/                          # Ollama集成 (5,000行)
│   │   ├── embeddings/                      # 向量嵌入 (3,000行)
│   │   ├── recommendations/                 # 智能推荐 (2,000行)
│   │   └── analytics/                       # 学习分析 (2,000行)
│   ├── task/                                # 任务服务 (8,000行)
│   │   ├── scheduler/                       # 任务调度
│   │   ├── queue/                           # 任务队列
│   │   └── workers/                         # 工作进程
│   └── deployment/                          # 部署配置 (20,000行)
│       ├── kubernetes/                      # K8s配置 (8,000行)
│       │   ├── helm/                        # Helm Charts
│       │   ├── agones/                      # Agones游戏服务器
│       │   └── monitoring/                  # 监控配置
│       ├── docker/                          # Docker配置 (6,000行)
│       │   ├── images/                      # 镜像构建
│       │   ├── compose/                     # Docker Compose
│       │   └── registry/                    # 镜像仓库
│       ├── edge/                            # 边缘计算 (3,000行)
│       │   ├── nodes/                       # 边缘节点
│       │   └── distribution/                # 内容分发
│       └── monitoring/                      # 监控运维 (3,000行)
│           ├── prometheus/                  # Prometheus
│           ├── grafana/                     # Grafana
│           └── logging/                     # 日志系统
├── client/                                  # 客户端应用 (15,000行)
│   ├── web/                                 # Web客户端 (10,000行)
│   │   ├── react/                           # React应用
│   │   ├── routing/                         # 路由系统
│   │   └── pwa/                             # PWA支持
│   └── mobile/                              # 移动端 (5,000行)
│       ├── ios/                             # iOS应用
│       └── android/                         # Android应用
└── shared/                                  # 共享模块 (14,232行)
    ├── common/                              # 通用工具 (8,000行)
    │   ├── types/                           # TypeScript类型
    │   ├── utils/                           # 工具函数
    │   ├── constants/                       # 常量定义
    │   └── validators/                      # 验证器
    ├── protocols/                           # 通信协议 (3,000行)
    │   ├── websocket/                       # WebSocket协议
    │   ├── webrtc/                          # WebRTC协议
    │   └── serialization/                   # 序列化协议
    └── projects/                            # 项目模板 (3,232行)
        ├── templates/                       # 项目模板
        ├── examples/                        # 示例项目
        └── education/                       # 教育模板

### 架构要点
- 身份认证：手机号+短信验证码（默认+86，可扩国际区号），JWT/Refresh，RBAC；防刷限流、风控与黑名单。
- 国际化：默认 zh-CN，支持 en-US；AntD LocaleProvider；内容/i18n 分层与运行时切换。
- ECS×网络：组件 Schema（版本化）+ 压缩序列化 + Interest Management；客户端预测/回滚；服务端权威合并；快照/增量混合同步。
- 物理×网络：客户端 cannon-es 作预测/手感，服务端 Rapier3D 做确定性与裁决；确定性Tick、输入序列、冲突解决、带宽自适应。
- XR/VR/AR：WebXR 标准、控制器/手势/空间锚、3D UI（WebLayer/HTML Layer），编辑器内预览与录制。
- 实时通信：Primus（WS）统一消息层，MediaSoup 做 A/V 低延迟；信令与会话握手分离；QoS/重传策略可配置。
- 数据层：MySQL（ACID/主业务）、Redis（会话/缓存/分布式锁/消息）、PostgreSQL（向量/相似度）、Minio（对象存储）。
- 微服务：Nest.js（业务）+ Feathers（实时服务）组合；服务间 gRPC/HTTP；K8s Service 发现与 HPA；边缘节点分发与近端缓存。
- 可观测性：OpenTelemetry/Prometheus/Grafana/ELK/Alertmanager；金丝雀与蓝绿；SLO/SLA 签署与合规审计。
- 多端发布：Web（PWA）；移动端（Capacitor/React Native 互选，首选 Capacitor 与 Web 复用）；桌面（Electron/Tauri，首选 Electron）。

### 依赖与版本建议（npm 管理）
- 前端：
  - react 18.2.0, react-dom 18.2.0, typescript 5.6.x, vite 5.4.8
  - three 0.176.0, @types/three 0.176.0, three-stdlib, postprocessing
  - antd 5.x, @ant-design/icons, @ant-design/colors
  - redux, @reduxjs/toolkit, react-redux
  - cannon-es, @dimforge/rapier3d-compat（仅桥接客户端诊断）
  - i18next/react-i18next 或 react-intl（二选一，推荐 i18next）
- 后端：
  - @nestjs/{core,common,platform-express}, @feathersjs/{feathers,koa,authentication}
  - mysql2, redis, pg
  - prisma 或 typeorm（二选一，推荐 Prisma 主库 + pgvector 客户端）
  - primus, mediasoup, mediasoup-client（客户端仓另管）
  - @rlay/rapier3d（或官方 rapier3d wasm 绑定）
  - minio, bullmq（任务队列）, zod/class-validator（契约校验）
- 工具与质量：eslint, prettier, vitest/jest, playwright, husky, lint-staged

### npm 工作空间
- 顶层 package.json 使用 npm workspaces：
  - workspaces: ["dl-engine/engine", "dl-engine/editor", "dl-engine/server/*"]
- 规范：严禁手改 package-lock.json；仅用 npm install/remove；Node 需 22.x；跨包引用用 workspace:*。

### 编码规范与前端状态
- React 全面使用 Hook 与函数组件；避免类组件。
- Hookstate useHookstate 替代 React useState（标准与最佳实践）。
- 组件按原子/复合/容器分层；SSR 非目标阶段，首期以 CSR + 预渲染为主。

### 安全与合规
- 传输：TLS1.3；数据：AES-256 at-rest；Secrets 用 K8s Secret + 外部密钥管家（如 KMS）。
- 隐私：GDPR/中国网安法要求，对未成年数据特别处理；日志脱敏；审计追踪可检索。
- 反滥用：验证码强度、IP/设备指纹、行为挑战；短信计费保护与速率限制。

### 交付与验收标准（每批）
- 可运行构建（npm scripts）、>=80% 单测覆盖（核心模块）、端到端冒烟用例、性能/安全基线、回滚与迁移脚本、中文文档。

---

## 分批次重构计划（每批约 20,000 行）

### 批次 01：工作区与骨架（npm）
- 目标：建立 dl-engine 三目录与 npm workspaces；统一 ESLint/Prettier/TSConfig/Vite 基建；CI 初始化。
- 交付：engine/editor/server 骨架与包清单；本地 Docker Compose 最小栈（MySQL/Redis/PG/Minio）。

### 批次 02：ECS 核心与组件模型（引擎）
- 目标：实体/组件/系统调度与生命周期；组件 Schema/序列化；最小渲染 Loop。
- 交付：entities/components/systems 初版；基准测试与单测。

### 批次 03：渲染与资源（Three.js）
- 目标：渲染器/场景/相机/灯光；GLTF/纹理/音频加载；后处理。
- 交付：three 集成与示例场景；性能基线（1080p@60fps）。

### 批次 04：客户端物理（cannon-es）
- 目标：cannon-es 集成；碰撞/约束；ECS 组件绑定；可视化调试。
- 交付：物理示例与编辑器内预览。

### 批次 05：服务端权威物理（Rapier3D）
- 目标：Rapier3D 实例服务；确定性 Tick；输入/状态流；冲突裁决。
- 交付：instance 服务 PoC；与客户端预测对齐的回滚机制。

### 批次 06：网络同步基础（Primus）
- 目标：会话/房间/兴趣管理；快照/增量压缩；延迟抖动控制与重连。
- 交付：协议契约（zod）、端到端同步样例与基准。

### 批次 07：状态管理（Redux + Hookstate 桥接）
- 目标：视图层与引擎状态桥；时间旅行/DevTools；持久化策略。
- 交付：全局 Store 与关键 Slice；跨页/多端一致性。

### 批次 08：认证服务（手机号登录）
- 目标：验证码生成/发送/验证；JWT/Refresh；RBAC；防刷限流。
- 交付：/auth/phone, /auth/verify, /auth/token API；审计与风控日志。

### 批次 09：国际化与UI基础（AntD 5）
- 目标：AntD 主题/布局；i18n（默认 zh-CN，支持 en-US）；表单/表格规范。
- 交付：基础 UI 组件库与设计令牌。

### 批次 10：编辑器核心（场景/属性/资产）
- 目标：视口/操作Gizmo/选择；属性检查器；资产浏览与导入。
- 交付：可保存/加载的场景 JSON；撤销/重做。

### 批次 11：可视化脚本（节点式）
- 目标：节点编辑器、连接、执行引擎；教育节点库（初集）。
- 交付：脚本→ECS 行为编译与运行；调试与Profiling。

### 批次 12：API 微服务（用户/项目/场景/资产）
- 目标：Nest.js 业务层 + Feathers 实时订阅；分页/搜索；审计日志。
- 交付：api 服务与 OpenAPI 文档；契约测试。

### 批次 13：媒体与实时（MediaSoup）
- 目标：上传/转码；A/V 房间；信令/编解码选择；录制回放。
- 交付：媒体服务与示例课堂。

### 批次 14：存储与对象服务（Minio）
- 目标：分片上传/断点续传；桶策略；CDN/边缘分发适配。
- 交付：storage 服务与策略模板。

### 批次 15：向量与AI（Ollama + PG）
- 目标：嵌入/检索；相似度搜索（pgvector）；推荐/学习分析基础。
- 交付：ai 服务与DSL；安全配额与缓存。

### 批次 16：协作与操作历史
- 目标：多人协作光标/锁；操作日志与回放；冲突解决。
- 交付：协作协议与回放播放器。

### 批次 17：Kubernetes + Helm + Agones
- 目标：Helm Charts（所有服务）；Agones 实例服/会话；HPA/探针/限额。
- 交付：一键部署脚本与仪表盘。

### 批次 18：监控与安全
- 目标：OpenTelemetry/Prometheus/Grafana；ELK；WAF/速率/审计。
- 交付：SLO 仪表与告警规则；渗透测试修复。

### 批次 19：XR 完成度提升
- 目标：WebXR 交互、空间UI、锚点；编辑器内VR 预览与录制。
- 交付：XR 模板项目与课程示例。

### 批次 20：移动端（iOS/Android）
- 目标：Capacitor 打包；原生权限/传感器桥接；离线缓存。
- 交付：TestFlight/内测包；移动端性能优化。

### 批次 21：桌面端与收尾
- 目标：Electron 打包；文件系统权限；自动更新；最终QA与性能压测。
- 交付：Win/macOS 安装包；发布与培训资料。

---

## 关键设计细节

### 手机号登录流程
- /auth/phone（发码，图形/行为验证码→短信）→ /auth/verify（换取短期令牌）→ /auth/token（发放 JWT/Refresh）。
- 防滥用：IP/设备/账户多维速率；黑名单；短信供应商降级切换。

### ECS×网络同步
- 组件定义→版本化 Schema→压缩（位集/变长数字）→增量广播；
- 客户端：输入预测 + 时间线缓存 + 回滚与重采样；
- 服务器：确定性 Tick（固定步长）、状态快照与校验；
- Interest：按视锥/距离/分区下发，QoS 等级化。

### 物理一致性
- 客户端 cannon-es 仅作预测与手感；
- 服务端 Rapier3D 保证裁决；同一初始种子与固定步长；
- 碰撞/约束事件进入事件总线，驱动 ECS 行为与脚本。

### Kubernetes 与边缘
- 服务通过 Helm 打包；Agones 管理实例服寿命周期与扩缩容；
- K8s Service 做发现与 L4 负载；网关（Ingress/Nginx）做 L7；
- 边缘节点：内容/快照/资源近端缓存；基于地理/网络就近路由。

---

## 质量与测试
- 单测：Vitest（前端/引擎），Jest（后端）；覆盖率≥80%。
- 集成：Playwright（端到端），Supertest（API），Cypress（可选）。
- 性能：渲染基准、网络延迟抖动测试、实例服并发与内存曲线。
- 安全：SQL 注入/XSS/CSRF/认证绕过/提权等常规自动化与灰盒。

## 路线图与里程碑
- M1（批1-3）：可运行的引擎最小闭环与场景渲染。
- M2（批4-8）：预测物理、网络同步、手机号登录与基础UI。
- M3（批9-12）：编辑器可用、核心业务API上线。
- M4（批13-17）：媒体/存储/AI、K8s+Helm+Agones 上线。
- M5（批18-21）：监控与安全、XR 完成度、移动/桌面发布。

## 风险与缓解
- 复杂度：批次化、契约驱动（OpenAPI/Schema）、严格接口稳定期。
- 性能：阶段性基线；性能回归门禁；火焰图与采样优化循环。
- 合规：数据主权/出境合规审查；未成年/课堂录像最小化与加密。

## 下一步
- 审阅本方案并确认版本；
- 同意后启动 批次01（工作区与骨架）并产出初始 commit 与流水线；
- 建立每周评审与里程碑复盘机制。
