/*
CPAL-1.0 License
*/

/**
 * DL-Engine API网关服务
 * 
 * 提供统一的API入口，包括：
 * - 路由管理
 * - 中间件处理
 * - 限流控制
 * - 负载均衡
 */

import { NestFactory } from '@nestjs/core'
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify'
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger'
import { AppModule } from './app.module'

async function bootstrap() {
  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter({ logger: true })
  )

  // 配置 CORS
  await app.register(require('@fastify/cors'), {
    origin: true,
    credentials: true
  })

  // 配置安全头
  await app.register(require('@fastify/helmet'))

  // 配置限流
  await app.register(require('@fastify/rate-limit'), {
    max: 100,
    timeWindow: '1 minute'
  })

  // 配置 Swagger
  const config = new DocumentBuilder()
    .setTitle('DL-Engine API Gateway')
    .setDescription('Digital Learning Engine API网关服务')
    .setVersion('1.0.0')
    .addBearerAuth()
    .build()
  
  const document = SwaggerModule.createDocument(app, config)
  SwaggerModule.setup('api', app, document)

  const port = process.env.PORT || 3030
  await app.listen(port, '0.0.0.0')
  
  console.log(`🚀 DL-Engine API Gateway is running on: http://localhost:${port}`)
  console.log(`📚 API Documentation: http://localhost:${port}/api`)
}

bootstrap().catch(err => {
  console.error('Failed to start API Gateway:', err)
  process.exit(1)
})
