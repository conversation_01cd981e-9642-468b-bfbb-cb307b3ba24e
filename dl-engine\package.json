{"name": "dl-engine-workspace", "version": "1.0.0", "description": "DL-Engine 工作空间 - 统一管理 engine、editor、server 模块", "private": true, "scripts": {"dev": "concurrently \"npm run dev:engine\" \"npm run dev:editor\" \"npm run dev:gateway\"", "dev:engine": "cd engine && npm run dev", "dev:editor": "cd editor && npm run dev", "dev:gateway": "cd server/gateway && npm run dev", "dev:auth": "cd server/auth && npm run dev", "dev:api": "cd server/api && npm run dev", "dev:instance": "cd server/instance && npm run dev", "dev:media": "cd server/media && npm run dev", "dev:storage": "cd server/storage && npm run dev", "dev:ai": "cd server/ai && npm run dev", "dev:task": "cd server/task && npm run dev", "build": "npm run build:engine && npm run build:editor && npm run build:server", "build:engine": "cd engine && npm run build", "build:editor": "cd editor && npm run build", "build:server": "npm run build:gateway && npm run build:auth && npm run build:api && npm run build:instance && npm run build:media && npm run build:storage && npm run build:ai && npm run build:task", "build:gateway": "cd server/gateway && npm run build", "build:auth": "cd server/auth && npm run build", "build:api": "cd server/api && npm run build", "build:instance": "cd server/instance && npm run build", "build:media": "cd server/media && npm run build", "build:storage": "cd server/storage && npm run build", "build:ai": "cd server/ai && npm run build", "build:task": "cd server/task && npm run build", "test": "npm run test:engine && npm run test:editor && npm run test:server", "test:engine": "cd engine && npm run test", "test:editor": "cd editor && npm run test", "test:server": "npm run test:gateway && npm run test:auth && npm run test:api && npm run test:instance && npm run test:media && npm run test:storage && npm run test:ai && npm run test:task", "test:gateway": "cd server/gateway && npm run test", "test:auth": "cd server/auth && npm run test", "test:api": "cd server/api && npm run test", "test:instance": "cd server/instance && npm run test", "test:media": "cd server/media && npm run test", "test:storage": "cd server/storage && npm run test", "test:ai": "cd server/ai && npm run test", "test:task": "cd server/task && npm run test", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "check-errors": "npm run check-errors:engine && npm run check-errors:editor && npm run check-errors:server", "check-errors:engine": "cd engine && npm run check-errors", "check-errors:editor": "cd editor && npm run check-errors", "check-errors:server": "npm run check-errors:gateway && npm run check-errors:auth && npm run check-errors:api && npm run check-errors:instance && npm run check-errors:media && npm run check-errors:storage && npm run check-errors:ai && npm run check-errors:task", "check-errors:gateway": "cd server/gateway && npm run check-errors", "check-errors:auth": "cd server/auth && npm run check-errors", "check-errors:api": "cd server/api && npm run check-errors", "check-errors:instance": "cd server/instance && npm run check-errors", "check-errors:media": "cd server/media && npm run check-errors", "check-errors:storage": "cd server/storage && npm run check-errors", "check-errors:ai": "cd server/ai && npm run check-errors", "check-errors:task": "cd server/task && npm run check-errors", "clean": "npm run clean:engine && npm run clean:editor && npm run clean:server", "clean:engine": "cd engine && npm run clean", "clean:editor": "cd editor && npm run clean", "clean:server": "npm run clean:gateway && npm run clean:auth && npm run clean:api && npm run clean:instance && npm run clean:media && npm run clean:storage && npm run clean:ai && npm run clean:task", "clean:gateway": "cd server/gateway && npm run clean", "clean:auth": "cd server/auth && npm run clean", "clean:api": "cd server/api && npm run clean", "clean:instance": "cd server/instance && npm run clean", "clean:media": "cd server/media && npm run clean", "clean:storage": "cd server/storage && npm run clean", "clean:ai": "cd server/ai && npm run clean", "clean:task": "cd server/task && npm run clean", "validate": "npm run lint && npm run check-errors && npm run test", "docker:up": "docker-compose -f ../docker-compose.yml up -d", "docker:down": "docker-compose -f ../docker-compose.yml down", "docker:logs": "docker-compose -f ../docker-compose.yml logs -f"}, "keywords": ["dl-engine", "digital-learning", "education", "3d", "vr", "ar", "webxr", "three.js", "react", "typescript"], "author": {"name": "Digital Learning Engine Team", "email": "<EMAIL>"}, "license": "CPAL-1.0", "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "concurrently": "7.6.0", "eslint": "9.5.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "prettier": "3.0.2", "typescript": "5.6.3"}, "engines": {"node": ">= 22.11.0"}}