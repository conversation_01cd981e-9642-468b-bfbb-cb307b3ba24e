/*
CPAL-1.0 License
*/

import * as THREE from 'three'
import * as CANNON from 'cannon-es'
import { PhysicsConfig } from '../core/config'
import { PhysicsBodyConfig } from './world'

/**
 * Cannon-es 物理引擎集成
 */
export class CannonPhysics {
  private world: CANNON.World
  private config: PhysicsConfig

  constructor(config: PhysicsConfig) {
    this.config = config
    this.world = new CANNON.World()
  }

  async initialize(): Promise<void> {
    // 设置重力
    this.world.gravity.set(...this.config.gravity)

    // 设置碰撞检测算法
    this.world.broadphase = new CANNON.SAPBroadphase(this.world)

    // 设置求解器
    this.world.solver.iterations = 10
    this.world.solver.tolerance = 0.1

    // 启用睡眠优化
    this.world.allowSleep = true

    // 设置默认接触材质
    this.world.defaultContactMaterial.friction = 0.4
    this.world.defaultContactMaterial.restitution = 0.3

    console.log('Cannon-es physics initialized')
  }

  update(deltaTime: number): void {
    this.world.step(this.config.timeStep, deltaTime, this.config.maxSubSteps)
  }

  createBody(config: PhysicsBodyConfig): CANNON.Body {
    const shape = this.createShape(config)
    const body = new CANNON.Body({
      mass: config.type === 'static' ? 0 : config.mass,
      shape,
      material: new CANNON.Material({
        friction: config.friction,
        restitution: config.restitution
      })
    })

    if (config.position) {
      body.position.set(config.position.x, config.position.y, config.position.z)
    }

    this.world.addBody(body)
    return body
  }

  private createShape(config: PhysicsBodyConfig): CANNON.Shape {
    const scale = config.scale || new THREE.Vector3(1, 1, 1)

    switch (config.shape) {
      case 'box':
        return new CANNON.Box(new CANNON.Vec3(
          scale.x * 0.5,
          scale.y * 0.5,
          scale.z * 0.5
        ))
      case 'sphere':
        return new CANNON.Sphere(scale.x * 0.5)
      case 'cylinder':
        return new CANNON.Cylinder(
          scale.x * 0.5, // radiusTop
          scale.x * 0.5, // radiusBottom
          scale.y,       // height
          8              // numSegments
        )
      case 'capsule':
        return new CANNON.Sphere(scale.x * 0.5) // 简化为球体
      case 'plane':
        return new CANNON.Plane()
      case 'mesh':
        // 对于复杂网格，使用凸包近似
        return new CANNON.Box(new CANNON.Vec3(
          scale.x * 0.5,
          scale.y * 0.5,
          scale.z * 0.5
        ))
      default:
        return new CANNON.Box(new CANNON.Vec3(
          scale.x * 0.5,
          scale.y * 0.5,
          scale.z * 0.5
        ))
    }
  }

  removeBody(body: CANNON.Body): void {
    this.world.removeBody(body)
  }

  setBodyPosition(body: CANNON.Body, position: THREE.Vector3): void {
    body.position.set(position.x, position.y, position.z)
  }

  getBodyPosition(body: CANNON.Body): THREE.Vector3 {
    return new THREE.Vector3(body.position.x, body.position.y, body.position.z)
  }

  setBodyRotation(body: CANNON.Body, rotation: THREE.Quaternion): void {
    body.quaternion.set(rotation.x, rotation.y, rotation.z, rotation.w)
  }

  getBodyRotation(body: CANNON.Body): THREE.Quaternion {
    return new THREE.Quaternion(body.quaternion.x, body.quaternion.y, body.quaternion.z, body.quaternion.w)
  }

  applyForce(body: CANNON.Body, force: THREE.Vector3, point?: THREE.Vector3): void {
    const cannonForce = new CANNON.Vec3(force.x, force.y, force.z)
    const cannonPoint = point ? new CANNON.Vec3(point.x, point.y, point.z) : undefined
    body.applyForce(cannonForce, cannonPoint)
  }

  applyImpulse(body: CANNON.Body, impulse: THREE.Vector3, point?: THREE.Vector3): void {
    const cannonImpulse = new CANNON.Vec3(impulse.x, impulse.y, impulse.z)
    const cannonPoint = point ? new CANNON.Vec3(point.x, point.y, point.z) : undefined
    body.applyImpulse(cannonImpulse, cannonPoint)
  }

  async destroy(): Promise<void> {
    // 清理所有物体
    while (this.world.bodies.length > 0) {
      this.world.removeBody(this.world.bodies[0])
    }
    console.log('Cannon-es physics destroyed')
  }
}
