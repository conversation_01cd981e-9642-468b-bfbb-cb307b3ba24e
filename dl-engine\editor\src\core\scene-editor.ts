/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import * as THREE from 'three'
import { Engine } from '../../engine/src'
import { EditorState } from './editor'

/**
 * 编辑器工具类型
 */
export enum EditorTool {
  SELECT = 'select',
  MOVE = 'move',
  ROTATE = 'rotate',
  SCALE = 'scale',
  BRUSH = 'brush'
}

/**
 * 视口模式
 */
export enum ViewportMode {
  PERSPECTIVE = 'perspective',
  ORTHOGRAPHIC = 'orthographic',
  TOP = 'top',
  FRONT = 'front',
  RIGHT = 'right'
}

/**
 * 场景编辑器
 * 
 * 负责3D场景的编辑功能
 */
export class SceneEditor {
  private engine?: Engine
  private scene?: THREE.Scene
  private camera?: THREE.Camera
  private renderer?: THREE.WebGLRenderer

  // 编辑工具
  private currentTool = EditorTool.SELECT
  private viewportMode = ViewportMode.PERSPECTIVE

  // 网格和辅助对象
  private gridHelper?: THREE.GridHelper
  private axesHelper?: THREE.AxesHelper
  private gizmos = new Map<string, THREE.Object3D>()

  // 选择系统
  private raycaster = new THREE.Raycaster()
  private mouse = new THREE.Vector2()
  private selectedObjects = new Set<THREE.Object3D>()

  // 回调函数
  public onObjectSelected?: (objectId: string, addToSelection: boolean) => void
  public onObjectDeselected?: (objectId: string) => void

  /**
   * 初始化场景编辑器
   */
  async initialize(engine: Engine): Promise<void> {
    this.engine = engine
    this.scene = engine.getSceneManager().getActiveScene()
    this.camera = engine.getCameraController().getActiveCamera()
    this.renderer = engine.getRenderingSystem().getRenderer()

    if (!this.scene || !this.camera || !this.renderer) {
      throw new Error('Failed to get required engine components')
    }

    // 创建辅助对象
    this.createHelpers()

    // 设置事件监听
    this.setupEventListeners()

    console.log('Scene editor initialized')
  }

  /**
   * 更新场景编辑器
   */
  update(deltaTime: number): void {
    // 更新 gizmos
    this.updateGizmos()
  }

  /**
   * 状态变化处理
   */
  onStateChange(oldState: EditorState, newState: EditorState): void {
    switch (newState) {
      case EditorState.PLAYING:
        this.hideEditorHelpers()
        break
      case EditorState.EDITING:
        this.showEditorHelpers()
        break
    }
  }

  /**
   * 设置当前工具
   */
  setTool(tool: EditorTool): void {
    this.currentTool = tool
    this.updateGizmos()
  }

  /**
   * 获取当前工具
   */
  getTool(): EditorTool {
    return this.currentTool
  }

  /**
   * 设置视口模式
   */
  setViewportMode(mode: ViewportMode): void {
    this.viewportMode = mode
    this.updateCamera()
  }

  /**
   * 获取视口模式
   */
  getViewportMode(): ViewportMode {
    return this.viewportMode
  }

  /**
   * 创建基础几何体
   */
  createPrimitive(type: 'cube' | 'sphere' | 'cylinder' | 'plane'): THREE.Object3D {
    let geometry: THREE.BufferGeometry
    
    switch (type) {
      case 'cube':
        geometry = new THREE.BoxGeometry(1, 1, 1)
        break
      case 'sphere':
        geometry = new THREE.SphereGeometry(0.5, 32, 16)
        break
      case 'cylinder':
        geometry = new THREE.CylinderGeometry(0.5, 0.5, 1, 32)
        break
      case 'plane':
        geometry = new THREE.PlaneGeometry(1, 1)
        break
      default:
        geometry = new THREE.BoxGeometry(1, 1, 1)
    }

    const material = new THREE.MeshStandardMaterial({ color: 0x888888 })
    const mesh = new THREE.Mesh(geometry, material)
    mesh.name = `${type}_${Date.now()}`
    mesh.userData.editorObject = true

    if (this.scene) {
      this.scene.add(mesh)
    }

    return mesh
  }

  /**
   * 删除对象
   */
  deleteObject(object: THREE.Object3D): void {
    if (this.scene && object.parent) {
      object.parent.remove(object)
      this.selectedObjects.delete(object)
      
      // 清理几何体和材质
      if (object instanceof THREE.Mesh) {
        object.geometry.dispose()
        if (Array.isArray(object.material)) {
          object.material.forEach(mat => mat.dispose())
        } else {
          object.material.dispose()
        }
      }
    }
  }

  /**
   * 复制对象
   */
  duplicateObject(object: THREE.Object3D): THREE.Object3D {
    const cloned = object.clone()
    cloned.name = `${object.name}_copy`
    cloned.position.add(new THREE.Vector3(1, 0, 1))
    
    if (this.scene && object.parent) {
      object.parent.add(cloned)
    }
    
    return cloned
  }

  /**
   * 聚焦到对象
   */
  focusOnObject(object: THREE.Object3D): void {
    if (!this.camera) return

    const box = new THREE.Box3().setFromObject(object)
    const center = box.getCenter(new THREE.Vector3())
    const size = box.getSize(new THREE.Vector3())
    
    const maxDim = Math.max(size.x, size.y, size.z)
    const distance = maxDim * 2

    if (this.camera instanceof THREE.PerspectiveCamera) {
      this.camera.position.copy(center)
      this.camera.position.z += distance
      this.camera.lookAt(center)
    }
  }

  /**
   * 序列化场景
   */
  serialize(): any {
    if (!this.scene) return null

    const sceneData = {
      objects: [] as any[],
      lights: [] as any[],
      cameras: [] as any[]
    }

    this.scene.traverse((object) => {
      if (object.userData.editorObject) {
        sceneData.objects.push(this.serializeObject(object))
      } else if (object instanceof THREE.Light) {
        sceneData.lights.push(this.serializeLight(object))
      } else if (object instanceof THREE.Camera) {
        sceneData.cameras.push(this.serializeCamera(object))
      }
    })

    return sceneData
  }

  /**
   * 反序列化场景
   */
  deserialize(data: any): void {
    if (!this.scene || !data) return

    // 清除现有对象
    const objectsToRemove: THREE.Object3D[] = []
    this.scene.traverse((object) => {
      if (object.userData.editorObject) {
        objectsToRemove.push(object)
      }
    })
    objectsToRemove.forEach(obj => this.scene!.remove(obj))

    // 加载对象
    if (data.objects) {
      data.objects.forEach((objData: any) => {
        const object = this.deserializeObject(objData)
        if (object) {
          this.scene!.add(object)
        }
      })
    }
  }

  /**
   * 创建辅助对象
   */
  private createHelpers(): void {
    if (!this.scene) return

    // 网格辅助
    this.gridHelper = new THREE.GridHelper(20, 20, 0x888888, 0x444444)
    this.scene.add(this.gridHelper)

    // 坐标轴辅助
    this.axesHelper = new THREE.AxesHelper(5)
    this.scene.add(this.axesHelper)
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    if (!this.renderer) return

    const canvas = this.renderer.domElement

    canvas.addEventListener('click', this.onMouseClick.bind(this))
    canvas.addEventListener('mousemove', this.onMouseMove.bind(this))
    canvas.addEventListener('keydown', this.onKeyDown.bind(this))
  }

  /**
   * 鼠标点击事件
   */
  private onMouseClick(event: MouseEvent): void {
    if (!this.camera || !this.scene) return

    // 更新鼠标位置
    const rect = (event.target as HTMLElement).getBoundingClientRect()
    this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

    // 射线检测
    this.raycaster.setFromCamera(this.mouse, this.camera)
    const intersects = this.raycaster.intersectObjects(this.scene.children, true)

    if (intersects.length > 0) {
      const object = intersects[0].object
      if (object.userData.editorObject) {
        const addToSelection = event.ctrlKey || event.metaKey
        this.selectObject(object, addToSelection)
      }
    } else {
      if (!event.ctrlKey && !event.metaKey) {
        this.clearSelection()
      }
    }
  }

  /**
   * 鼠标移动事件
   */
  private onMouseMove(event: MouseEvent): void {
    // 处理 gizmo 交互
  }

  /**
   * 键盘事件
   */
  private onKeyDown(event: KeyboardEvent): void {
    switch (event.key) {
      case 'Delete':
        this.deleteSelectedObjects()
        break
      case 'Escape':
        this.clearSelection()
        break
    }
  }

  /**
   * 选择对象
   */
  private selectObject(object: THREE.Object3D, addToSelection = false): void {
    if (!addToSelection) {
      this.clearSelection()
    }
    
    this.selectedObjects.add(object)
    this.updateObjectSelection(object, true)
    
    if (this.onObjectSelected) {
      this.onObjectSelected(object.uuid, addToSelection)
    }
  }

  /**
   * 清除选择
   */
  private clearSelection(): void {
    this.selectedObjects.forEach(object => {
      this.updateObjectSelection(object, false)
    })
    this.selectedObjects.clear()
  }

  /**
   * 删除选中对象
   */
  private deleteSelectedObjects(): void {
    const objectsToDelete = Array.from(this.selectedObjects)
    objectsToDelete.forEach(object => {
      this.deleteObject(object)
    })
    this.selectedObjects.clear()
  }

  /**
   * 更新对象选择状态
   */
  private updateObjectSelection(object: THREE.Object3D, selected: boolean): void {
    // 这里可以添加选择高亮效果
    object.userData.selected = selected
  }

  /**
   * 更新 gizmos
   */
  private updateGizmos(): void {
    // 根据当前工具和选中对象更新 gizmos
  }

  /**
   * 更新相机
   */
  private updateCamera(): void {
    // 根据视口模式更新相机
  }

  /**
   * 显示编辑器辅助对象
   */
  private showEditorHelpers(): void {
    if (this.gridHelper) this.gridHelper.visible = true
    if (this.axesHelper) this.axesHelper.visible = true
  }

  /**
   * 隐藏编辑器辅助对象
   */
  private hideEditorHelpers(): void {
    if (this.gridHelper) this.gridHelper.visible = false
    if (this.axesHelper) this.axesHelper.visible = false
  }

  /**
   * 序列化对象
   */
  private serializeObject(object: THREE.Object3D): any {
    return {
      uuid: object.uuid,
      name: object.name,
      type: object.type,
      position: object.position.toArray(),
      rotation: object.rotation.toArray(),
      scale: object.scale.toArray(),
      userData: object.userData
    }
  }

  /**
   * 反序列化对象
   */
  private deserializeObject(data: any): THREE.Object3D | null {
    // 这里应该根据对象类型创建相应的对象
    return null
  }

  /**
   * 序列化光源
   */
  private serializeLight(light: THREE.Light): any {
    return {
      uuid: light.uuid,
      type: light.type,
      color: light.color.getHex(),
      intensity: light.intensity
    }
  }

  /**
   * 序列化相机
   */
  private serializeCamera(camera: THREE.Camera): any {
    return {
      uuid: camera.uuid,
      type: camera.type,
      position: camera.position.toArray(),
      rotation: camera.rotation.toArray()
    }
  }

  /**
   * 销毁场景编辑器
   */
  async destroy(): Promise<void> {
    // 清理事件监听
    if (this.renderer) {
      const canvas = this.renderer.domElement
      canvas.removeEventListener('click', this.onMouseClick.bind(this))
      canvas.removeEventListener('mousemove', this.onMouseMove.bind(this))
      canvas.removeEventListener('keydown', this.onKeyDown.bind(this))
    }

    // 清理辅助对象
    if (this.scene) {
      if (this.gridHelper) this.scene.remove(this.gridHelper)
      if (this.axesHelper) this.scene.remove(this.axesHelper)
    }

    this.selectedObjects.clear()
    this.gizmos.clear()

    console.log('Scene editor destroyed')
  }
}
