/*
CPAL-1.0 License
*/

import { Injectable } from '@nestjs/common'

@Injectable()
export class MetricsService {
  getMetrics() {
    return {
      requests: {
        total: 1000,
        success: 950,
        error: 50
      },
      response_time: {
        avg: 120,
        p95: 250,
        p99: 500
      },
      memory: {
        used: process.memoryUsage().heapUsed,
        total: process.memoryUsage().heapTotal
      },
      cpu: {
        usage: process.cpuUsage()
      }
    }
  }
}
