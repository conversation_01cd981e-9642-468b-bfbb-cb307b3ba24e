/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import * as THREE from 'three'
import { PhysicsConfig } from '../core/config'
import { CannonPhysics } from './cannon'
import { RapierPhysics } from './rapier'
import { CollisionDetection } from './collision'
import { ConstraintSystem } from './constraints'
import { SpatialQueries } from './queries'
import { PhysicsNetworking } from './networking'

/**
 * 物理体类型
 */
export enum PhysicsBodyType {
  STATIC = 'static',
  DYNAMIC = 'dynamic',
  KINEMATIC = 'kinematic'
}

/**
 * 物理形状类型
 */
export enum PhysicsShapeType {
  BOX = 'box',
  SPHERE = 'sphere',
  CYLINDER = 'cylinder',
  CAPSULE = 'capsule',
  PLANE = 'plane',
  MESH = 'mesh',
  HEIGHTFIELD = 'heightfield'
}

/**
 * 物理体配置
 */
export interface PhysicsBodyConfig {
  type: PhysicsBodyType
  shape: PhysicsShapeType
  mass: number
  friction: number
  restitution: number
  position?: THREE.Vector3
  rotation?: THREE.Quaternion
  scale?: THREE.Vector3
  isSensor?: boolean
  isEnabled?: boolean
}

/**
 * 物理世界
 * 
 * 管理物理模拟的主要类
 */
export class PhysicsWorld {
  private config: PhysicsConfig
  private cannonPhysics?: CannonPhysics
  private rapierPhysics?: RapierPhysics
  private collisionDetection: CollisionDetection
  private constraintSystem: ConstraintSystem
  private spatialQueries: SpatialQueries
  private physicsNetworking: PhysicsNetworking

  private isInitialized = false
  private isRunning = false
  private scene?: THREE.Scene

  // 物理体管理
  private bodies = new Map<number, any>()
  private bodyConfigs = new Map<number, PhysicsBodyConfig>()

  constructor(config: PhysicsConfig) {
    this.config = config
    this.collisionDetection = new CollisionDetection()
    this.constraintSystem = new ConstraintSystem()
    this.spatialQueries = new SpatialQueries()
    this.physicsNetworking = new PhysicsNetworking()
  }

  /**
   * 初始化物理世界
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.warn('Physics world is already initialized')
      return
    }

    try {
      // 根据配置选择物理引擎
      if (this.config.engine === 'cannon-es') {
        this.cannonPhysics = new CannonPhysics(this.config)
        await this.cannonPhysics.initialize()
      } else if (this.config.engine === 'rapier3d') {
        this.rapierPhysics = new RapierPhysics(this.config)
        await this.rapierPhysics.initialize()
      }

      // 初始化子系统
      await this.collisionDetection.initialize()
      await this.constraintSystem.initialize()
      await this.spatialQueries.initialize()
      await this.physicsNetworking.initialize()

      this.isInitialized = true
      console.log('Physics world initialized with engine:', this.config.engine)
    } catch (error) {
      console.error('Failed to initialize physics world:', error)
      throw error
    }
  }

  /**
   * 启动物理模拟
   */
  start(): void {
    if (!this.isInitialized) {
      throw new Error('Physics world must be initialized before starting')
    }

    this.isRunning = true
    console.log('Physics world started')
  }

  /**
   * 停止物理模拟
   */
  stop(): void {
    this.isRunning = false
    console.log('Physics world stopped')
  }

  /**
   * 更新物理世界
   */
  update(deltaTime: number): void {
    if (!this.isRunning) {
      return
    }

    // 更新物理引擎
    if (this.cannonPhysics) {
      this.cannonPhysics.update(deltaTime)
    } else if (this.rapierPhysics) {
      this.rapierPhysics.update(deltaTime)
    }

    // 更新子系统
    this.collisionDetection.update(deltaTime)
    this.constraintSystem.update(deltaTime)
    this.spatialQueries.update(deltaTime)
    this.physicsNetworking.update(deltaTime)

    // 同步物理体到渲染对象
    this.syncPhysicsToRender()
  }

  /**
   * 创建物理体
   */
  createBody(id: number, config: PhysicsBodyConfig): any {
    let body: any

    if (this.cannonPhysics) {
      body = this.cannonPhysics.createBody(config)
    } else if (this.rapierPhysics) {
      body = this.rapierPhysics.createBody(config)
    } else {
      throw new Error('No physics engine initialized')
    }

    this.bodies.set(id, body)
    this.bodyConfigs.set(id, config)

    return body
  }

  /**
   * 获取物理体
   */
  getBody(id: number): any | undefined {
    return this.bodies.get(id)
  }

  /**
   * 删除物理体
   */
  removeBody(id: number): boolean {
    const body = this.bodies.get(id)
    if (!body) {
      return false
    }

    if (this.cannonPhysics) {
      this.cannonPhysics.removeBody(body)
    } else if (this.rapierPhysics) {
      this.rapierPhysics.removeBody(body)
    }

    this.bodies.delete(id)
    this.bodyConfigs.delete(id)
    return true
  }

  /**
   * 设置物理体位置
   */
  setBodyPosition(id: number, position: THREE.Vector3): void {
    const body = this.bodies.get(id)
    if (!body) return

    if (this.cannonPhysics) {
      this.cannonPhysics.setBodyPosition(body, position)
    } else if (this.rapierPhysics) {
      this.rapierPhysics.setBodyPosition(body, position)
    }
  }

  /**
   * 获取物理体位置
   */
  getBodyPosition(id: number): THREE.Vector3 | undefined {
    const body = this.bodies.get(id)
    if (!body) return undefined

    if (this.cannonPhysics) {
      return this.cannonPhysics.getBodyPosition(body)
    } else if (this.rapierPhysics) {
      return this.rapierPhysics.getBodyPosition(body)
    }

    return undefined
  }

  /**
   * 设置物理体旋转
   */
  setBodyRotation(id: number, rotation: THREE.Quaternion): void {
    const body = this.bodies.get(id)
    if (!body) return

    if (this.cannonPhysics) {
      this.cannonPhysics.setBodyRotation(body, rotation)
    } else if (this.rapierPhysics) {
      this.rapierPhysics.setBodyRotation(body, rotation)
    }
  }

  /**
   * 获取物理体旋转
   */
  getBodyRotation(id: number): THREE.Quaternion | undefined {
    const body = this.bodies.get(id)
    if (!body) return undefined

    if (this.cannonPhysics) {
      return this.cannonPhysics.getBodyRotation(body)
    } else if (this.rapierPhysics) {
      return this.rapierPhysics.getBodyRotation(body)
    }

    return undefined
  }

  /**
   * 应用力到物理体
   */
  applyForce(id: number, force: THREE.Vector3, point?: THREE.Vector3): void {
    const body = this.bodies.get(id)
    if (!body) return

    if (this.cannonPhysics) {
      this.cannonPhysics.applyForce(body, force, point)
    } else if (this.rapierPhysics) {
      this.rapierPhysics.applyForce(body, force, point)
    }
  }

  /**
   * 应用冲量到物理体
   */
  applyImpulse(id: number, impulse: THREE.Vector3, point?: THREE.Vector3): void {
    const body = this.bodies.get(id)
    if (!body) return

    if (this.cannonPhysics) {
      this.cannonPhysics.applyImpulse(body, impulse, point)
    } else if (this.rapierPhysics) {
      this.rapierPhysics.applyImpulse(body, impulse, point)
    }
  }

  /**
   * 设置场景引用
   */
  setScene(scene: THREE.Scene): void {
    this.scene = scene
  }

  /**
   * 获取碰撞检测系统
   */
  getCollisionDetection(): CollisionDetection {
    return this.collisionDetection
  }

  /**
   * 获取约束系统
   */
  getConstraintSystem(): ConstraintSystem {
    return this.constraintSystem
  }

  /**
   * 获取空间查询系统
   */
  getSpatialQueries(): SpatialQueries {
    return this.spatialQueries
  }

  /**
   * 同步物理体到渲染对象
   */
  private syncPhysicsToRender(): void {
    if (!this.scene) return

    // 遍历所有物理体，同步位置和旋转到对应的渲染对象
    for (const [id, body] of this.bodies) {
      const position = this.getBodyPosition(id)
      const rotation = this.getBodyRotation(id)

      if (position && rotation) {
        // 查找对应的渲染对象并更新其变换
        const renderObject = this.scene.getObjectByProperty('userData.physicsId', id)
        if (renderObject) {
          renderObject.position.copy(position)
          renderObject.quaternion.copy(rotation)
        }
      }
    }
  }

  /**
   * 获取物理统计信息
   */
  getStats(): {
    bodies: number
    constraints: number
    collisions: number
  } {
    return {
      bodies: this.bodies.size,
      constraints: this.constraintSystem.getConstraintCount(),
      collisions: this.collisionDetection.getCollisionCount()
    }
  }

  /**
   * 销毁物理世界
   */
  async destroy(): Promise<void> {
    this.stop()

    // 销毁所有物理体
    this.bodies.clear()
    this.bodyConfigs.clear()

    // 销毁子系统
    await this.physicsNetworking.destroy()
    await this.spatialQueries.destroy()
    await this.constraintSystem.destroy()
    await this.collisionDetection.destroy()

    // 销毁物理引擎
    if (this.cannonPhysics) {
      await this.cannonPhysics.destroy()
      this.cannonPhysics = undefined
    }
    if (this.rapierPhysics) {
      await this.rapierPhysics.destroy()
      this.rapierPhysics = undefined
    }

    this.isInitialized = false
    console.log('Physics world destroyed')
  }
}
