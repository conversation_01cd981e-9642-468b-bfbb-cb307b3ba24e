/*
CPAL-1.0 License
*/

import React from 'react'
import ReactDOM from 'react-dom/client'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import './index.css'

// 临时的 App 组件，后续会被完整的编辑器应用替换
const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <div style={{ 
        height: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        flexDirection: 'column',
        background: '#f0f2f5'
      }}>
        <h1 style={{ color: '#1890ff', marginBottom: '16px' }}>
          DL-Engine 编辑器
        </h1>
        <p style={{ color: '#666', fontSize: '16px' }}>
          Digital Learning Engine - 面向教育场景的3D/VR/AR应用开发平台
        </p>
        <p style={{ color: '#999', fontSize: '14px', marginTop: '16px' }}>
          编辑器正在开发中...
        </p>
      </div>
    </ConfigProvider>
  )
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
