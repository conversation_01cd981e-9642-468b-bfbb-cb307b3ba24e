/*
CPAL-1.0 License
*/

import { NestFactory } from '@nestjs/core'
import { AppModule } from './app.module'
import { Logger } from '@nestjs/common'

async function bootstrap() {
  const logger = new Logger('StorageService')
  const app = await NestFactory.create(AppModule)
  
  const port = process.env.PORT || 3035
  await app.listen(port)
  logger.log(`💾 Storage Service is running on: http://localhost:${port}`)
}

bootstrap()
