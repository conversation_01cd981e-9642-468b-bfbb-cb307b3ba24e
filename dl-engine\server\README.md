# DL-Engine 服务器端

这是 DL-Engine 的服务器端模块，采用微服务架构设计。

## 服务架构

- `gateway/` - API网关 (15,000行)
  - `routing/` - 路由系统
  - `middleware/` - 中间件
  - `rate-limiting/` - 限流控制
  - `load-balancing/` - 负载均衡

- `auth/` - 认证服务 (20,000行)
  - `phone/` - 手机号登录 (8,000行)
    - `sms/` - 短信验证
    - `verification/` - 验证码
    - `registration/` - 注册流程
  - `jwt/` - JWT令牌 (6,000行)
  - `oauth/` - OAuth集成 (4,000行)
  - `permissions/` - 权限管理 (2,000行)

- `api/` - API服务 (65,000行)
  - `users/` - 用户管理 (12,000行)
  - `projects/` - 项目管理 (10,000行)
  - `scenes/` - 场景管理 (8,000行)
  - `assets/` - 资产管理 (8,000行)
  - `social/` - 社交功能 (6,000行)
  - `education/` - 教育功能 (8,000行)
    - `courses/` - 课程管理
    - `assignments/` - 作业系统
    - `assessments/` - 评估系统
    - `analytics/` - 学习分析
  - `collaboration/` - 协作功能 (5,000行)
  - `notifications/` - 通知系统 (4,000行)
  - `monitoring/` - 监控接口 (4,000行)

- `instance/` - 实例服务 (25,000行)
  - `world/` - 世界实例 (10,000行)
  - `networking/` - 网络同步 (8,000行)
  - `physics/` - 物理同步 (4,000行)
  - `scaling/` - 扩缩容 (3,000行)

- `media/` - 媒体服务 (20,000行)
  - `upload/` - 文件上传 (6,000行)
  - `processing/` - 媒体处理 (8,000行)
    - `image/` - 图像处理
    - `video/` - 视频处理
    - `audio/` - 音频处理
    - `3d-models/` - 3D模型处理
  - `streaming/` - 流媒体 (4,000行)
  - `cdn/` - CDN集成 (2,000行)

- `storage/` - 存储服务 (15,000行)
  - `minio/` - Minio对象存储 (6,000行)
  - `database/` - 数据库服务 (5,000行)
    - `mysql/` - MySQL主库
    - `redis/` - Redis缓存
    - `postgresql/` - PostgreSQL向量库
  - `backup/` - 备份系统 (2,000行)
  - `migration/` - 数据迁移 (2,000行)

- `ai/` - AI服务 (12,000行)
  - `ollama/` - Ollama集成 (5,000行)
  - `embeddings/` - 向量嵌入 (3,000行)
  - `recommendations/` - 智能推荐 (2,000行)
  - `analytics/` - 学习分析 (2,000行)

- `task/` - 任务服务 (8,000行)
  - `scheduler/` - 任务调度
  - `queue/` - 任务队列
  - `workers/` - 工作进程

- `deployment/` - 部署配置 (20,000行)
  - `kubernetes/` - K8s配置 (8,000行)
    - `helm/` - Helm Charts
    - `agones/` - Agones游戏服务器
    - `monitoring/` - 监控配置
  - `docker/` - Docker配置 (6,000行)
    - `images/` - 镜像构建
    - `compose/` - Docker Compose
    - `registry/` - 镜像仓库
  - `edge/` - 边缘计算 (3,000行)
    - `nodes/` - 边缘节点
    - `distribution/` - 内容分发
  - `monitoring/` - 监控运维 (3,000行)
    - `prometheus/` - Prometheus
    - `grafana/` - Grafana
    - `logging/` - 日志系统

## 技术栈

- Node.js 22
- Nest.js (业务服务)
- FeathersJS (实时服务)
- MySQL 8.0 (主数据库)
- Redis 7 (缓存)
- PostgreSQL 16 (向量数据库)
- Minio (对象存储)
- Primus (WebSocket)
- MediaSoup (WebRTC)
- Rapier3D 0.11.2 (服务端物理)
- Ollama (AI推理)
- Docker & Kubernetes
- Helm & Agones
