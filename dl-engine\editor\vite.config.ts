/*
CPAL-1.0 License
*/

import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'DLEngineEditor',
      fileName: (format) => `dl-engine-editor.${format}.js`,
      formats: ['es', 'cjs']
    },
    rollupOptions: {
      external: [
        'react',
        'react-dom',
        'three',
        '@types/three',
        'antd',
        '@ant-design/icons',
        '@ant-design/colors',
        'react-router-dom',
        'redux',
        '@reduxjs/toolkit',
        'react-redux',
        'i18next',
        'react-i18next'
      ],
      output: {
        globals: {
          'react': 'React',
          'react-dom': 'ReactDOM',
          'three': 'THREE',
          'antd': 'antd',
          '@ant-design/icons': 'AntdIcons',
          'react-router-dom': 'ReactRouterDOM',
          'redux': 'Redux',
          '@reduxjs/toolkit': 'RTK',
          'react-redux': 'ReactRedux',
          'i18next': 'i18next',
          'react-i18next': 'ReactI18next'
        }
      }
    },
    sourcemap: true,
    minify: false
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@dl-engine/engine': resolve(__dirname, '../engine/src')
    }
  },
  server: {
    port: 3001,
    host: true
  },
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./vitest.setup.ts']
  }
})
