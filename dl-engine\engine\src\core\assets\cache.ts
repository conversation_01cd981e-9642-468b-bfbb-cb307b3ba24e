/*
CPAL-1.0 License
*/

import { AssetMetadata } from './manager'

/**
 * 缓存项接口
 */
interface CacheItem {
  data: any
  metadata: AssetMetadata
  lastAccessed: number
  accessCount: number
}

/**
 * 缓存策略枚举
 */
export enum CacheStrategy {
  LRU = 'lru',
  LFU = 'lfu',
  FIFO = 'fifo'
}

/**
 * 资产缓存系统
 *
 * 支持多种缓存策略的高效资产缓存
 */
export class AssetCache {
  private cache = new Map<string, CacheItem>()
  private maxSize = 100 * 1024 * 1024 // 100MB
  private currentSize = 0
  private strategy = CacheStrategy.LRU
  private maxItems = 1000

  /**
   * 初始化缓存系统
   */
  async initialize(config?: {
    maxSize?: number
    maxItems?: number
    strategy?: CacheStrategy
  }): Promise<void> {
    if (config) {
      this.maxSize = config.maxSize || this.maxSize
      this.maxItems = config.maxItems || this.maxItems
      this.strategy = config.strategy || this.strategy
    }

    console.log(`Asset cache initialized with strategy: ${this.strategy}`)
  }

  /**
   * 存储资产到缓存
   */
  async store(id: string, asset: any, metadata: AssetMetadata): Promise<void> {
    const now = Date.now()
    const item: CacheItem = {
      data: asset,
      metadata,
      lastAccessed: now,
      accessCount: 1
    }

    // 如果已存在，先移除旧的
    if (this.cache.has(id)) {
      this.remove(id)
    }

    this.cache.set(id, item)

    if (metadata.size) {
      this.currentSize += metadata.size
    }

    // 检查缓存大小和数量限制
    this.enforceLimit()
  }

  /**
   * 从缓存获取资产
   */
  get(id: string): any | undefined {
    const item = this.cache.get(id)
    if (!item) {
      return undefined
    }

    // 更新访问信息
    item.lastAccessed = Date.now()
    item.accessCount++

    return item.data
  }

  /**
   * 从缓存移除资产
   */
  remove(id: string): boolean {
    const item = this.cache.get(id)
    if (!item) {
      return false
    }

    if (item.metadata.size) {
      this.currentSize -= item.metadata.size
    }

    return this.cache.delete(id)
  }

  /**
   * 检查缓存中是否存在资产
   */
  has(id: string): boolean {
    return this.cache.has(id)
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    size: number
    maxSize: number
    items: number
    maxItems: number
    hitRate: number
  } {
    return {
      size: this.currentSize,
      maxSize: this.maxSize,
      items: this.cache.size,
      maxItems: this.maxItems,
      hitRate: 0 // TODO: 实现命中率统计
    }
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear()
    this.currentSize = 0
  }

  /**
   * 强制执行缓存限制
   */
  private enforceLimit(): void {
    // 检查数量限制
    while (this.cache.size > this.maxItems) {
      this.evictOne()
    }

    // 检查大小限制
    while (this.currentSize > this.maxSize && this.cache.size > 0) {
      this.evictOne()
    }
  }

  /**
   * 驱逐一个缓存项
   */
  private evictOne(): void {
    let victimId: string | undefined

    switch (this.strategy) {
      case CacheStrategy.LRU:
        victimId = this.findLRUVictim()
        break
      case CacheStrategy.LFU:
        victimId = this.findLFUVictim()
        break
      case CacheStrategy.FIFO:
        victimId = this.findFIFOVictim()
        break
    }

    if (victimId) {
      this.remove(victimId)
    }
  }

  /**
   * 查找 LRU 受害者
   */
  private findLRUVictim(): string | undefined {
    let oldestTime = Date.now()
    let victimId: string | undefined

    for (const [id, item] of this.cache) {
      if (item.lastAccessed < oldestTime) {
        oldestTime = item.lastAccessed
        victimId = id
      }
    }

    return victimId
  }

  /**
   * 查找 LFU 受害者
   */
  private findLFUVictim(): string | undefined {
    let lowestCount = Infinity
    let victimId: string | undefined

    for (const [id, item] of this.cache) {
      if (item.accessCount < lowestCount) {
        lowestCount = item.accessCount
        victimId = id
      }
    }

    return victimId
  }

  /**
   * 查找 FIFO 受害者
   */
  private findFIFOVictim(): string | undefined {
    // 返回第一个插入的项（Map 保持插入顺序）
    return this.cache.keys().next().value
  }

  /**
   * 销毁缓存系统
   */
  async destroy(): Promise<void> {
    this.clear()
    console.log('Asset cache destroyed')
  }
}
