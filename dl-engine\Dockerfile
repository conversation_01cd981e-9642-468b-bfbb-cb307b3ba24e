# DL-Engine Multi-stage Dockerfile
# 支持构建 engine、editor、server 各个模块

ARG NODE_VERSION=22.11.0

# 基础镜像
FROM node:${NODE_VERSION}-alpine AS base
WORKDIR /app
RUN apk add --no-cache libc6-compat
RUN npm install -g npm@latest

# 依赖安装阶段
FROM base AS deps
COPY package*.json ./
COPY dl-engine/package*.json ./dl-engine/
COPY dl-engine/engine/package*.json ./dl-engine/engine/
COPY dl-engine/editor/package*.json ./dl-engine/editor/
COPY dl-engine/server/gateway/package*.json ./dl-engine/server/gateway/
COPY dl-engine/server/auth/package*.json ./dl-engine/server/auth/
COPY dl-engine/server/api/package*.json ./dl-engine/server/api/
COPY dl-engine/server/instance/package*.json ./dl-engine/server/instance/
COPY dl-engine/server/media/package*.json ./dl-engine/server/media/
COPY dl-engine/server/storage/package*.json ./dl-engine/server/storage/
COPY dl-engine/server/ai/package*.json ./dl-engine/server/ai/
COPY dl-engine/server/task/package*.json ./dl-engine/server/task/

RUN npm ci --only=production && npm cache clean --force

# 构建阶段
FROM base AS builder
COPY . .
COPY --from=deps /app/node_modules ./node_modules

# 构建所有模块
RUN cd dl-engine && npm run build

# Engine 运行时镜像
FROM base AS engine
COPY --from=builder /app/dl-engine/engine/dist ./engine/dist
COPY --from=builder /app/dl-engine/engine/package*.json ./engine/
COPY --from=deps /app/dl-engine/engine/node_modules ./engine/node_modules
WORKDIR /app/engine
EXPOSE 3000
CMD ["npm", "start"]

# Editor 运行时镜像
FROM nginx:alpine AS editor
COPY --from=builder /app/dl-engine/editor/dist /usr/share/nginx/html
COPY dl-engine/editor/nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]

# Gateway 服务镜像
FROM base AS gateway
COPY --from=builder /app/dl-engine/server/gateway/dist ./dist
COPY --from=builder /app/dl-engine/server/gateway/package*.json ./
COPY --from=deps /app/dl-engine/server/gateway/node_modules ./node_modules
EXPOSE 3030
CMD ["npm", "start"]

# Auth 服务镜像
FROM base AS auth
COPY --from=builder /app/dl-engine/server/auth/dist ./dist
COPY --from=builder /app/dl-engine/server/auth/package*.json ./
COPY --from=deps /app/dl-engine/server/auth/node_modules ./node_modules
EXPOSE 3031
CMD ["npm", "start"]

# API 服务镜像
FROM base AS api
COPY --from=builder /app/dl-engine/server/api/dist ./dist
COPY --from=builder /app/dl-engine/server/api/package*.json ./
COPY --from=deps /app/dl-engine/server/api/node_modules ./node_modules
EXPOSE 3032
CMD ["npm", "start"]

# Instance 服务镜像
FROM base AS instance
COPY --from=builder /app/dl-engine/server/instance/dist ./dist
COPY --from=builder /app/dl-engine/server/instance/package*.json ./
COPY --from=deps /app/dl-engine/server/instance/node_modules ./node_modules
EXPOSE 3033
CMD ["npm", "start"]

# Media 服务镜像
FROM base AS media
RUN apk add --no-cache ffmpeg
COPY --from=builder /app/dl-engine/server/media/dist ./dist
COPY --from=builder /app/dl-engine/server/media/package*.json ./
COPY --from=deps /app/dl-engine/server/media/node_modules ./node_modules
EXPOSE 3034
CMD ["npm", "start"]

# Storage 服务镜像
FROM base AS storage
COPY --from=builder /app/dl-engine/server/storage/dist ./dist
COPY --from=builder /app/dl-engine/server/storage/package*.json ./
COPY --from=deps /app/dl-engine/server/storage/node_modules ./node_modules
EXPOSE 3035
CMD ["npm", "start"]

# AI 服务镜像
FROM base AS ai
COPY --from=builder /app/dl-engine/server/ai/dist ./dist
COPY --from=builder /app/dl-engine/server/ai/package*.json ./
COPY --from=deps /app/dl-engine/server/ai/node_modules ./node_modules
EXPOSE 3036
CMD ["npm", "start"]

# Task 服务镜像
FROM base AS task
COPY --from=builder /app/dl-engine/server/task/dist ./dist
COPY --from=builder /app/dl-engine/server/task/package*.json ./
COPY --from=deps /app/dl-engine/server/task/node_modules ./node_modules
EXPOSE 3037
CMD ["npm", "start"]
