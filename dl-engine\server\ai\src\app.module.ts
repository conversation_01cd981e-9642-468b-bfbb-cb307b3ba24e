/*
CPAL-1.0 License
*/

import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { AIModule } from './ai/ai.module'
import { OllamaModule } from './ollama/ollama.module'
import { HealthModule } from './health/health.module'

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    AIModule,
    OllamaModule,
    HealthModule
  ]
})
export class AppModule {}
