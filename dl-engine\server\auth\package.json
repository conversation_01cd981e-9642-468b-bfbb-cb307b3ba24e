{"name": "@dl-engine/server-auth", "version": "1.0.0", "description": "DL-Engine 认证服务 - 手机号登录、JWT、OAuth、权限管理", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "build": "tsc", "build:watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "check-errors": "tsc --noEmit", "validate": "npm run lint && npm run check-errors && npm run test"}, "keywords": ["dl-engine", "auth", "authentication", "authorization", "jwt", "o<PERSON>h", "phone-login", "sms", "rbac"], "author": {"name": "Digital Learning Engine Team", "email": "<EMAIL>"}, "license": "CPAL-1.0", "dependencies": {"@nestjs/common": "^10.4.8", "@nestjs/core": "^10.4.8", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-fastify": "^10.4.8", "@nestjs/swagger": "^8.0.5", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "fastify": "^5.1.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "mysql2": "^3.11.4", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "typeorm": "^0.3.20", "zod": "^3.23.8"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.11.0", "@types/passport": "^1.0.16", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitest/coverage-istanbul": "2.1.1", "eslint": "9.5.0", "prettier": "3.0.2", "rimraf": "4.4.0", "ts-node-dev": "^2.0.0", "typescript": "5.6.3", "vitest": "2.1.1"}, "engines": {"node": ">= 22.11.0"}}