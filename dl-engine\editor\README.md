# DL-Engine 在线编辑器

这是 DL-Engine 的在线编辑器模块，提供可视化的3D/VR/AR内容创作工具。

## 主要组件

- `core/` - 编辑器核心
  - `scene-editor/` - 场景编辑器
    - `viewport/` - 3D视口
    - `gizmos/` - 操作工具
    - `selection/` - 选择系统
    - `camera/` - 相机控制
  - `properties/` - 属性面板
    - `inspector/` - 检查器
    - `materials/` - 材质编辑
    - `components/` - 组件编辑
  - `assets/` - 资产浏览器
    - `browser/` - 文件浏览
    - `preview/` - 预览系统
    - `import/` - 导入工具
  - `hierarchy/` - 层次结构
    - `tree/` - 场景树
    - `search/` - 搜索过滤
    - `operations/` - 操作命令
  - `toolbar/` - 工具栏

- `ui/` - 编辑器界面
  - `components/` - UI组件
    - `panels/` - 面板组件
    - `dialogs/` - 对话框
    - `menus/` - 菜单系统
    - `forms/` - 表单组件
  - `layout/` - 布局系统
    - `docking/` - 停靠面板
    - `tabs/` - 标签页
    - `splitters/` - 分割器
  - `themes/` - 主题系统
    - `light/` - 浅色主题
    - `dark/` - 深色主题
    - `education/` - 教育主题
  - `i18n/` - 国际化
    - `zh-CN/` - 中文语言包
    - `en-US/` - 英文语言包
    - `localization/` - 本地化工具

- `visual-script/` - 可视化脚本
  - `editor/` - 节点编辑器
    - `canvas/` - 画布系统
    - `nodes/` - 节点渲染
    - `connections/` - 连接系统
    - `minimap/` - 小地图
  - `engine/` - 脚本引擎
    - `execution/` - 执行引擎
    - `debugging/` - 调试系统
    - `profiling/` - 性能分析
  - `library/` - 节点库
    - `basic/` - 基础节点
    - `math/` - 数学节点
    - `logic/` - 逻辑节点
    - `education/` - 教育节点

- `plugins/` - 插件系统
  - `api/` - 插件API
  - `loader/` - 插件加载器
  - `marketplace/` - 插件市场

## 技术栈

- React 18.2.0
- TypeScript 5.6.3
- Vite 5.4.8
- Ant Design 5.x
- Redux + @reduxjs/toolkit
- i18next (国际化)
- Three.js 0.176.0 (3D渲染)
