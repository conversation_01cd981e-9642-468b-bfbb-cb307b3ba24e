version: '3.8'

# DL-Engine 开发环境基础设施
# 包含 MySQL、Redis、PostgreSQL、Minio 等核心服务

services:
  # MySQL 8.0 - 主数据库
  mysql:
    image: mysql:8.0
    container_name: dl-engine-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: dl_engine_root_2024
      MYSQL_DATABASE: dl_engine
      MYSQL_USER: dl_engine
      MYSQL_PASSWORD: dl_engine_pass_2024
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - '3306:3306'
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=256M
      --max-connections=200
      --sql-mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/mysql/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      timeout: 20s
      retries: 10
    networks:
      - dl-engine-network

  # Redis 7 - 缓存和会话存储
  redis:
    image: redis:7-alpine
    container_name: dl-engine-redis
    restart: unless-stopped
    ports:
      - '6379:6379'
    command: >
      redis-server
      --appendonly yes
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    volumes:
      - redis_data:/data
      - ./scripts/redis/redis.conf:/usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 3s
      retries: 5
    networks:
      - dl-engine-network

  # PostgreSQL 16 - 向量数据库 (pgvector)
  postgres:
    image: pgvector/pgvector:pg16
    container_name: dl-engine-postgres
    restart: unless-stopped
    environment:
      POSTGRES_PASSWORD: dl_vector_root_2024
      POSTGRES_USER: dl_vector
      POSTGRES_DB: dl_vector
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - '5432:5432'
    volumes:
      - pg_data:/var/lib/postgresql/data
      - ./scripts/postgres/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dl_vector -d dl_vector"]
      timeout: 5s
      retries: 5
    networks:
      - dl-engine-network

  # Minio - 对象存储
  minio:
    image: minio/minio:RELEASE.2024-09-22T00-00-00Z
    container_name: dl-engine-minio
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: dl_engine_minio
      MINIO_ROOT_PASSWORD: dl_engine_minio_2024
      MINIO_BROWSER_REDIRECT_URL: http://localhost:9001
    command: server /data --console-address ":9001"
    ports:
      - '9000:9000'  # API 端口
      - '9001:9001'  # 控制台端口
    volumes:
      - minio_data:/data
      - ./scripts/minio/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      timeout: 5s
      retries: 5
    networks:
      - dl-engine-network

  # Ollama - AI 推理服务 (可选，用于开发环境)
  ollama:
    image: ollama/ollama:latest
    container_name: dl-engine-ollama
    restart: unless-stopped
    ports:
      - '11434:11434'
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    networks:
      - dl-engine-network
    profiles:
      - ai  # 使用 --profile ai 启动

# 网络配置
networks:
  dl-engine-network:
    driver: bridge
    name: dl-engine-network

# 数据卷
volumes:
  mysql_data:
    name: dl-engine-mysql-data
  redis_data:
    name: dl-engine-redis-data
  pg_data:
    name: dl-engine-postgres-data
  minio_data:
    name: dl-engine-minio-data
  ollama_data:
    name: dl-engine-ollama-data

