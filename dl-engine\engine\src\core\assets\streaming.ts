/*
CPAL-1.0 License
*/

import { AssetType } from './manager'

/**
 * 预加载项接口
 */
interface PreloadItem {
  id: string
  url: string
  type: AssetType
  priority: number
  status: 'pending' | 'loading' | 'loaded' | 'error'
  size?: number
  progress?: number
}

/**
 * 流式加载配置
 */
interface StreamingConfig {
  maxConcurrentLoads: number
  priorityThreshold: number
  chunkSize: number
  enableServiceWorker: boolean
}

/**
 * 流式加载管理器
 *
 * 支持优先级队列和并发控制的资产流式加载
 */
export class StreamingManager {
  private preloadQueue = new Map<string, PreloadItem>()
  private loadingItems = new Set<string>()
  private isSupported = false
  private config: StreamingConfig
  private serviceWorker?: ServiceWorker

  constructor() {
    this.config = {
      maxConcurrentLoads: 4,
      priorityThreshold: 0.5,
      chunkSize: 64 * 1024, // 64KB
      enableServiceWorker: true
    }
  }

  /**
   * 初始化流式加载管理器
   */
  async initialize(config?: Partial<StreamingConfig>): Promise<void> {
    if (config) {
      this.config = { ...this.config, ...config }
    }

    this.isSupported = 'serviceWorker' in navigator && 'ReadableStream' in window

    if (this.isSupported && this.config.enableServiceWorker) {
      await this.setupServiceWorker()
    }

    console.log('Streaming manager initialized')
  }

  /**
   * 设置 Service Worker
   */
  private async setupServiceWorker(): Promise<void> {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js')
      this.serviceWorker = registration.active || registration.installing || registration.waiting
      console.log('Service Worker registered for streaming')
    } catch (error) {
      console.warn('Failed to register Service Worker:', error)
    }
  }

  /**
   * 检查是否支持流式加载
   */
  isStreamingSupported(): boolean {
    return this.isSupported
  }

  /**
   * 预加载资产
   */
  async preload(
    id: string,
    url: string,
    type: AssetType,
    priority: number = 0.5
  ): Promise<void> {
    const item: PreloadItem = {
      id,
      url,
      type,
      priority,
      status: 'pending'
    }

    this.preloadQueue.set(id, item)

    // 启动加载处理
    this.processQueue()
  }

  /**
   * 处理预加载队列
   */
  private async processQueue(): Promise<void> {
    // 如果已达到最大并发数，等待
    if (this.loadingItems.size >= this.config.maxConcurrentLoads) {
      return
    }

    // 按优先级排序队列
    const sortedItems = Array.from(this.preloadQueue.values())
      .filter(item => item.status === 'pending')
      .sort((a, b) => b.priority - a.priority)

    for (const item of sortedItems) {
      if (this.loadingItems.size >= this.config.maxConcurrentLoads) {
        break
      }

      this.loadItem(item)
    }
  }

  /**
   * 加载单个项目
   */
  private async loadItem(item: PreloadItem): Promise<void> {
    this.loadingItems.add(item.id)
    item.status = 'loading'

    try {
      if (this.serviceWorker) {
        await this.streamWithServiceWorker(item)
      } else {
        await this.streamWithFetch(item)
      }

      item.status = 'loaded'
    } catch (error) {
      item.status = 'error'
      console.error(`Failed to preload asset ${item.id}:`, error)
    } finally {
      this.loadingItems.delete(item.id)
      // 继续处理队列
      this.processQueue()
    }
  }

  /**
   * 使用 Service Worker 流式加载
   */
  private async streamWithServiceWorker(item: PreloadItem): Promise<void> {
    // 发送消息给 Service Worker 开始预加载
    if (this.serviceWorker) {
      this.serviceWorker.postMessage({
        type: 'PRELOAD_ASSET',
        id: item.id,
        url: item.url,
        assetType: item.type
      })
    }
  }

  /**
   * 使用 Fetch API 流式加载
   */
  private async streamWithFetch(item: PreloadItem): Promise<void> {
    const response = await fetch(item.url)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('ReadableStream not supported')
    }

    const contentLength = parseInt(response.headers.get('content-length') || '0')
    let receivedLength = 0

    try {
      while (true) {
        const { done, value } = await reader.read()

        if (done) break

        receivedLength += value.length
        item.progress = contentLength > 0 ? receivedLength / contentLength : 0

        // 这里可以将数据块存储到缓存中
      }
    } finally {
      reader.releaseLock()
    }
  }

  /**
   * 获取预加载状态
   */
  getPreloadStatus(id: string): PreloadItem | undefined {
    return this.preloadQueue.get(id)
  }

  /**
   * 取消预加载
   */
  cancelPreload(id: string): boolean {
    const item = this.preloadQueue.get(id)
    if (item && item.status === 'pending') {
      this.preloadQueue.delete(id)
      return true
    }
    return false
  }

  /**
   * 获取队列统计
   */
  getQueueStats(): {
    total: number
    pending: number
    loading: number
    loaded: number
    error: number
  } {
    const stats = {
      total: this.preloadQueue.size,
      pending: 0,
      loading: 0,
      loaded: 0,
      error: 0
    }

    for (const item of this.preloadQueue.values()) {
      stats[item.status]++
    }

    return stats
  }

  /**
   * 检查是否支持
   */
  isSupported(): boolean {
    return this.isSupported
  }

  /**
   * 销毁流式加载管理器
   */
  async destroy(): Promise<void> {
    this.preloadQueue.clear()
    this.loadingItems.clear()
    console.log('Streaming manager destroyed')
  }
}
