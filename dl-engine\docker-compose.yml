version: '3.8'

# DL-Engine 服务编排
# 包含所有微服务的完整部署配置

services:
  # 编辑器前端
  editor:
    build:
      context: .
      dockerfile: Dockerfile
      target: editor
    container_name: dl-engine-editor
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - gateway
    networks:
      - dl-engine-network

  # API 网关
  gateway:
    build:
      context: .
      dockerfile: Dockerfile
      target: gateway
    container_name: dl-engine-gateway
    restart: unless-stopped
    ports:
      - "3030:3030"
    environment:
      - NODE_ENV=production
      - PORT=3030
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    networks:
      - dl-engine-network

  # 认证服务
  auth:
    build:
      context: .
      dockerfile: Dockerfile
      target: auth
    container_name: dl-engine-auth
    restart: unless-stopped
    ports:
      - "3031:3031"
    environment:
      - NODE_ENV=production
      - PORT=3031
      - DATABASE_URL=mysql://dl_engine:dl_engine_pass_2024@mysql:3306/dl_engine
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET:-dl_engine_jwt_secret_2024}
    depends_on:
      - mysql
      - redis
    networks:
      - dl-engine-network

  # API 服务
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: api
    container_name: dl-engine-api
    restart: unless-stopped
    ports:
      - "3032:3032"
    environment:
      - NODE_ENV=production
      - PORT=3032
      - DATABASE_URL=mysql://dl_engine:dl_engine_pass_2024@mysql:3306/dl_engine
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mysql
      - redis
    networks:
      - dl-engine-network

  # 实例服务
  instance:
    build:
      context: .
      dockerfile: Dockerfile
      target: instance
    container_name: dl-engine-instance
    restart: unless-stopped
    ports:
      - "3033:3033"
    environment:
      - NODE_ENV=production
      - PORT=3033
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    networks:
      - dl-engine-network

  # 媒体服务
  media:
    build:
      context: .
      dockerfile: Dockerfile
      target: media
    container_name: dl-engine-media
    restart: unless-stopped
    ports:
      - "3034:3034"
    environment:
      - NODE_ENV=production
      - PORT=3034
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=dl_engine_minio
      - MINIO_SECRET_KEY=dl_engine_minio_2024
    depends_on:
      - minio
    networks:
      - dl-engine-network

  # 存储服务
  storage:
    build:
      context: .
      dockerfile: Dockerfile
      target: storage
    container_name: dl-engine-storage
    restart: unless-stopped
    ports:
      - "3035:3035"
    environment:
      - NODE_ENV=production
      - PORT=3035
      - DATABASE_URL=mysql://dl_engine:dl_engine_pass_2024@mysql:3306/dl_engine
      - POSTGRES_URL=********************************************************/dl_vector
      - REDIS_URL=redis://redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=dl_engine_minio
      - MINIO_SECRET_KEY=dl_engine_minio_2024
    depends_on:
      - mysql
      - postgres
      - redis
      - minio
    networks:
      - dl-engine-network

  # AI 服务
  ai:
    build:
      context: .
      dockerfile: Dockerfile
      target: ai
    container_name: dl-engine-ai
    restart: unless-stopped
    ports:
      - "3036:3036"
    environment:
      - NODE_ENV=production
      - PORT=3036
      - POSTGRES_URL=********************************************************/dl_vector
      - OLLAMA_URL=http://ollama:11434
    depends_on:
      - postgres
    networks:
      - dl-engine-network
    profiles:
      - ai

  # 任务服务
  task:
    build:
      context: .
      dockerfile: Dockerfile
      target: task
    container_name: dl-engine-task
    restart: unless-stopped
    ports:
      - "3037:3037"
    environment:
      - NODE_ENV=production
      - PORT=3037
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    networks:
      - dl-engine-network

  # 基础设施服务（从根目录的 docker-compose.yml 继承）
  mysql:
    extends:
      file: ../docker-compose.yml
      service: mysql
    networks:
      - dl-engine-network

  redis:
    extends:
      file: ../docker-compose.yml
      service: redis
    networks:
      - dl-engine-network

  postgres:
    extends:
      file: ../docker-compose.yml
      service: postgres
    networks:
      - dl-engine-network

  minio:
    extends:
      file: ../docker-compose.yml
      service: minio
    networks:
      - dl-engine-network

  ollama:
    extends:
      file: ../docker-compose.yml
      service: ollama
    networks:
      - dl-engine-network
    profiles:
      - ai

# 网络配置
networks:
  dl-engine-network:
    driver: bridge
    name: dl-engine-network
