/*
CPAL-1.0 License
*/

import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { SceneModule } from './scene/scene.module'
import { ProjectModule } from './project/project.module'
import { HealthModule } from './health/health.module'

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    SceneModule,
    ProjectModule,
    HealthModule
  ]
})
export class AppModule {}
