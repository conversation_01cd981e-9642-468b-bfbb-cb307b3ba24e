/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import * as THREE from 'three'

/**
 * 材质类型枚举
 */
export enum MaterialType {
  BASIC = 'basic',
  STANDARD = 'standard',
  PHYSICAL = 'physical',
  TOON = 'toon',
  LAMBERT = 'lambert',
  PHONG = 'phong',
  CUSTOM = 'custom'
}

/**
 * 材质配置接口
 */
export interface MaterialConfig {
  type: MaterialType
  name?: string
  color?: THREE.ColorRepresentation
  map?: THREE.Texture
  normalMap?: THREE.Texture
  roughnessMap?: THREE.Texture
  metalnessMap?: THREE.Texture
  emissiveMap?: THREE.Texture
  aoMap?: THREE.Texture
  roughness?: number
  metalness?: number
  transparent?: boolean
  opacity?: number
  side?: THREE.Side
  [key: string]: any
}

/**
 * 材质管理器
 * 
 * 负责创建、缓存和管理所有材质
 */
export class MaterialManager {
  private materials = new Map<string, THREE.Material>()
  private textures = new Map<string, THREE.Texture>()
  private renderer?: THREE.WebGLRenderer
  private textureLoader = new THREE.TextureLoader()

  /**
   * 初始化材质管理器
   */
  async initialize(renderer: THREE.WebGLRenderer): Promise<void> {
    this.renderer = renderer
    
    // 创建默认材质
    this.createDefaultMaterials()
    
    console.log('Material manager initialized')
  }

  /**
   * 创建材质
   */
  createMaterial(id: string, config: MaterialConfig): THREE.Material {
    let material: THREE.Material

    switch (config.type) {
      case MaterialType.BASIC:
        material = this.createBasicMaterial(config)
        break
      case MaterialType.STANDARD:
        material = this.createStandardMaterial(config)
        break
      case MaterialType.PHYSICAL:
        material = this.createPhysicalMaterial(config)
        break
      case MaterialType.TOON:
        material = this.createToonMaterial(config)
        break
      case MaterialType.LAMBERT:
        material = this.createLambertMaterial(config)
        break
      case MaterialType.PHONG:
        material = this.createPhongMaterial(config)
        break
      default:
        material = this.createStandardMaterial(config)
    }

    material.name = config.name || id
    this.materials.set(id, material)
    
    return material
  }

  /**
   * 获取材质
   */
  getMaterial(id: string): THREE.Material | undefined {
    return this.materials.get(id)
  }

  /**
   * 删除材质
   */
  deleteMaterial(id: string): boolean {
    const material = this.materials.get(id)
    if (material) {
      material.dispose()
      return this.materials.delete(id)
    }
    return false
  }

  /**
   * 加载纹理
   */
  async loadTexture(url: string, id?: string): Promise<THREE.Texture> {
    const textureId = id || url
    
    // 检查缓存
    const cached = this.textures.get(textureId)
    if (cached) {
      return cached
    }

    // 加载新纹理
    return new Promise((resolve, reject) => {
      this.textureLoader.load(
        url,
        (texture) => {
          texture.wrapS = THREE.RepeatWrapping
          texture.wrapT = THREE.RepeatWrapping
          texture.colorSpace = THREE.SRGBColorSpace
          this.textures.set(textureId, texture)
          resolve(texture)
        },
        undefined,
        reject
      )
    })
  }

  /**
   * 获取纹理
   */
  getTexture(id: string): THREE.Texture | undefined {
    return this.textures.get(id)
  }

  /**
   * 克隆材质
   */
  cloneMaterial(sourceId: string, newId: string): THREE.Material | undefined {
    const source = this.materials.get(sourceId)
    if (!source) {
      return undefined
    }

    const cloned = source.clone()
    cloned.name = newId
    this.materials.set(newId, cloned)
    
    return cloned
  }

  /**
   * 获取所有材质
   */
  getAllMaterials(): Map<string, THREE.Material> {
    return new Map(this.materials)
  }

  /**
   * 清理未使用的材质
   */
  cleanup(): void {
    // 这里可以实现引用计数和自动清理逻辑
    console.log('Material cleanup completed')
  }

  /**
   * 销毁材质管理器
   */
  async destroy(): Promise<void> {
    // 销毁所有材质
    for (const material of this.materials.values()) {
      material.dispose()
    }
    this.materials.clear()

    // 销毁所有纹理
    for (const texture of this.textures.values()) {
      texture.dispose()
    }
    this.textures.clear()

    console.log('Material manager destroyed')
  }

  /**
   * 创建默认材质
   */
  private createDefaultMaterials(): void {
    // 默认标准材质
    this.createMaterial('default', {
      type: MaterialType.STANDARD,
      color: 0xffffff,
      roughness: 0.5,
      metalness: 0.0
    })

    // 默认基础材质
    this.createMaterial('basic', {
      type: MaterialType.BASIC,
      color: 0xffffff
    })

    // 错误材质（洋红色）
    this.createMaterial('error', {
      type: MaterialType.BASIC,
      color: 0xff00ff
    })
  }

  /**
   * 创建基础材质
   */
  private createBasicMaterial(config: MaterialConfig): THREE.MeshBasicMaterial {
    return new THREE.MeshBasicMaterial({
      color: config.color,
      map: config.map,
      transparent: config.transparent,
      opacity: config.opacity,
      side: config.side
    })
  }

  /**
   * 创建标准材质
   */
  private createStandardMaterial(config: MaterialConfig): THREE.MeshStandardMaterial {
    return new THREE.MeshStandardMaterial({
      color: config.color,
      map: config.map,
      normalMap: config.normalMap,
      roughnessMap: config.roughnessMap,
      metalnessMap: config.metalnessMap,
      emissiveMap: config.emissiveMap,
      aoMap: config.aoMap,
      roughness: config.roughness,
      metalness: config.metalness,
      transparent: config.transparent,
      opacity: config.opacity,
      side: config.side
    })
  }

  /**
   * 创建物理材质
   */
  private createPhysicalMaterial(config: MaterialConfig): THREE.MeshPhysicalMaterial {
    return new THREE.MeshPhysicalMaterial({
      color: config.color,
      map: config.map,
      normalMap: config.normalMap,
      roughnessMap: config.roughnessMap,
      metalnessMap: config.metalnessMap,
      emissiveMap: config.emissiveMap,
      aoMap: config.aoMap,
      roughness: config.roughness,
      metalness: config.metalness,
      transparent: config.transparent,
      opacity: config.opacity,
      side: config.side,
      clearcoat: config.clearcoat || 0,
      clearcoatRoughness: config.clearcoatRoughness || 0
    })
  }

  /**
   * 创建卡通材质
   */
  private createToonMaterial(config: MaterialConfig): THREE.MeshToonMaterial {
    return new THREE.MeshToonMaterial({
      color: config.color,
      map: config.map,
      transparent: config.transparent,
      opacity: config.opacity,
      side: config.side
    })
  }

  /**
   * 创建 Lambert 材质
   */
  private createLambertMaterial(config: MaterialConfig): THREE.MeshLambertMaterial {
    return new THREE.MeshLambertMaterial({
      color: config.color,
      map: config.map,
      transparent: config.transparent,
      opacity: config.opacity,
      side: config.side
    })
  }

  /**
   * 创建 Phong 材质
   */
  private createPhongMaterial(config: MaterialConfig): THREE.MeshPhongMaterial {
    return new THREE.MeshPhongMaterial({
      color: config.color,
      map: config.map,
      normalMap: config.normalMap,
      transparent: config.transparent,
      opacity: config.opacity,
      side: config.side,
      shininess: config.shininess || 30
    })
  }
}
