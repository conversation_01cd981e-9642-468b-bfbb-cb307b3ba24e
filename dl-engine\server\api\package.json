{"name": "@dl-engine/server-api", "version": "1.0.0", "description": "DL-Engine API服务 - 用户、项目、场景、资产、教育功能管理", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "build": "tsc", "build:watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "check-errors": "tsc --noEmit", "validate": "npm run lint && npm run check-errors && npm run test", "migrate": "typeorm migration:run", "migrate:revert": "typeorm migration:revert", "migrate:generate": "typeorm migration:generate", "schema:sync": "typeorm schema:sync"}, "keywords": ["dl-engine", "api", "rest-api", "graphql", "users", "projects", "scenes", "assets", "education", "collaboration"], "author": {"name": "Digital Learning Engine Team", "email": "<EMAIL>"}, "license": "CPAL-1.0", "dependencies": {"@nestjs/common": "^10.4.8", "@nestjs/core": "^10.4.8", "@nestjs/graphql": "^12.2.1", "@nestjs/platform-fastify": "^10.4.8", "@nestjs/swagger": "^8.0.5", "@nestjs/typeorm": "^10.0.2", "apollo-server-fastify": "^3.12.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "fastify": "^5.1.0", "graphql": "^16.9.0", "ioredis": "^5.4.1", "mysql2": "^3.11.4", "typeorm": "^0.3.20", "uuid": "9.0.0", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22.11.0", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitest/coverage-istanbul": "2.1.1", "eslint": "9.5.0", "prettier": "3.0.2", "rimraf": "4.4.0", "ts-node-dev": "^2.0.0", "typescript": "5.6.3", "vitest": "2.1.1"}, "engines": {"node": ">= 22.11.0"}}