{"name": "@dl-engine/server-ai", "version": "1.0.0", "description": "DL-Engine AI服务 - Ollama集成、向量嵌入、智能推荐、学习分析", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "build": "tsc", "build:watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "check-errors": "tsc --noEmit", "validate": "npm run lint && npm run check-errors && npm run test"}, "keywords": ["dl-engine", "ai", "ollama", "embeddings", "vector-database", "recommendations", "analytics", "nlp", "machine-learning"], "author": {"name": "Digital Learning Engine Team", "email": "<EMAIL>"}, "license": "CPAL-1.0", "dependencies": {"@nestjs/common": "^10.4.8", "@nestjs/core": "^10.4.8", "@nestjs/platform-fastify": "^10.4.8", "@nestjs/swagger": "^8.0.5", "fastify": "^5.1.0", "ollama": "^0.5.16", "pg": "^8.13.1", "pgvector": "^0.2.0", "uuid": "9.0.0", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22.11.0", "@types/pg": "^8.11.10", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitest/coverage-istanbul": "2.1.1", "eslint": "9.5.0", "prettier": "3.0.2", "rimraf": "4.4.0", "ts-node-dev": "^2.0.0", "typescript": "5.6.3", "vitest": "2.1.1"}, "engines": {"node": ">= 22.11.0"}}