/*
CPAL-1.0 License
*/

import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { InstanceModule } from './instance/instance.module'
import { SessionModule } from './session/session.module'
import { HealthModule } from './health/health.module'

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    InstanceModule,
    SessionModule,
    HealthModule
  ]
})
export class AppModule {}
