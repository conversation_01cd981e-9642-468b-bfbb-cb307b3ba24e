/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

/**
 * 实体信息接口
 */
export interface EntityInfo {
  id: number
  name?: string
  active: boolean
  created: number
  destroyed?: number
}

/**
 * 实体管理器
 * 
 * 负责实体的创建、销毁和生命周期管理
 */
export class EntityManager {
  private entities = new Map<number, EntityInfo>()
  private destroyedEntities = new Set<number>()
  private nextEntityId = 1
  private recycledIds: number[] = []

  /**
   * 初始化实体管理器
   */
  async initialize(): Promise<void> {
    console.log('Entity manager initialized')
  }

  /**
   * 创建实体
   */
  createEntity(name?: string): number {
    // 尝试回收 ID
    const id = this.recycledIds.length > 0 
      ? this.recycledIds.pop()! 
      : this.nextEntityId++

    const entity: EntityInfo = {
      id,
      name,
      active: true,
      created: Date.now()
    }

    this.entities.set(id, entity)
    return id
  }

  /**
   * 销毁实体
   */
  destroyEntity(entityId: number): boolean {
    const entity = this.entities.get(entityId)
    if (!entity) {
      return false
    }

    entity.active = false
    entity.destroyed = Date.now()
    this.destroyedEntities.add(entityId)
    
    return true
  }

  /**
   * 获取实体信息
   */
  getEntity(entityId: number): EntityInfo | undefined {
    return this.entities.get(entityId)
  }

  /**
   * 检查实体是否存在且活跃
   */
  isEntityActive(entityId: number): boolean {
    const entity = this.entities.get(entityId)
    return entity ? entity.active : false
  }

  /**
   * 设置实体名称
   */
  setEntityName(entityId: number, name: string): boolean {
    const entity = this.entities.get(entityId)
    if (entity) {
      entity.name = name
      return true
    }
    return false
  }

  /**
   * 获取实体名称
   */
  getEntityName(entityId: number): string | undefined {
    const entity = this.entities.get(entityId)
    return entity?.name
  }

  /**
   * 获取所有活跃实体
   */
  getActiveEntities(): number[] {
    const activeEntities: number[] = []
    for (const [id, entity] of this.entities) {
      if (entity.active) {
        activeEntities.push(id)
      }
    }
    return activeEntities
  }

  /**
   * 获取实体数量
   */
  getEntityCount(): number {
    let count = 0
    for (const entity of this.entities.values()) {
      if (entity.active) {
        count++
      }
    }
    return count
  }

  /**
   * 清理已销毁的实体
   */
  cleanup(): void {
    for (const entityId of this.destroyedEntities) {
      this.entities.delete(entityId)
      this.recycledIds.push(entityId)
    }
    this.destroyedEntities.clear()
  }

  /**
   * 获取所有实体信息
   */
  getAllEntities(): Map<number, EntityInfo> {
    return new Map(this.entities)
  }

  /**
   * 销毁实体管理器
   */
  async destroy(): Promise<void> {
    this.entities.clear()
    this.destroyedEntities.clear()
    this.recycledIds.length = 0
    this.nextEntityId = 1
    
    console.log('Entity manager destroyed')
  }
}
