/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import * as THREE from 'three'
import { GLTFLoader } from './loaders'
import { AssetCache } from './cache'
import { StreamingManager } from './streaming'

/**
 * 资产类型枚举
 */
export enum AssetType {
  TEXTURE = 'texture',
  MODEL = 'model',
  AUDIO = 'audio',
  VIDEO = 'video',
  MATERIAL = 'material',
  ANIMATION = 'animation',
  SCENE = 'scene'
}

/**
 * 资产状态枚举
 */
export enum AssetStatus {
  PENDING = 'pending',
  LOADING = 'loading',
  LOADED = 'loaded',
  ERROR = 'error'
}

/**
 * 资产元数据接口
 */
export interface AssetMetadata {
  id: string
  url: string
  type: AssetType
  status: AssetStatus
  size?: number
  lastModified?: Date
  dependencies?: string[]
  tags?: string[]
}

/**
 * 加载进度接口
 */
export interface LoadProgress {
  loaded: number
  total: number
  percentage: number
}

/**
 * 资产管理器
 * 
 * 统一管理所有类型的资产加载和缓存
 */
export class AssetManager {
  private assets = new Map<string, any>()
  private metadata = new Map<string, AssetMetadata>()
  private loadingPromises = new Map<string, Promise<any>>()
  
  // 子系统
  private cache: AssetCache
  private streaming: StreamingManager
  private gltfLoader: GLTFLoader

  // 加载器
  private textureLoader = new THREE.TextureLoader()
  private audioLoader = new THREE.AudioLoader()
  private fileLoader = new THREE.FileLoader()

  // 事件回调
  private onProgress?: (progress: LoadProgress) => void
  private onError?: (error: Error, assetId: string) => void

  constructor() {
    this.cache = new AssetCache()
    this.streaming = new StreamingManager()
    this.gltfLoader = new GLTFLoader()
  }

  /**
   * 初始化资产管理器
   */
  async initialize(): Promise<void> {
    await this.cache.initialize()
    await this.streaming.initialize()
    
    console.log('Asset manager initialized')
  }

  /**
   * 加载资产
   */
  async loadAsset(id: string, url: string, type: AssetType): Promise<any> {
    // 检查是否已经加载
    if (this.assets.has(id)) {
      return this.assets.get(id)
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(id)) {
      return this.loadingPromises.get(id)
    }

    // 创建元数据
    const metadata: AssetMetadata = {
      id,
      url,
      type,
      status: AssetStatus.PENDING
    }
    this.metadata.set(id, metadata)

    // 开始加载
    const loadPromise = this.performLoad(id, url, type)
    this.loadingPromises.set(id, loadPromise)

    try {
      const asset = await loadPromise
      this.assets.set(id, asset)
      metadata.status = AssetStatus.LOADED
      this.loadingPromises.delete(id)
      
      // 缓存资产
      await this.cache.store(id, asset, metadata)
      
      return asset
    } catch (error) {
      metadata.status = AssetStatus.ERROR
      this.loadingPromises.delete(id)
      
      if (this.onError) {
        this.onError(error as Error, id)
      }
      
      throw error
    }
  }

  /**
   * 批量加载资产
   */
  async loadAssets(assets: Array<{ id: string; url: string; type: AssetType }>): Promise<Map<string, any>> {
    const results = new Map<string, any>()
    const promises = assets.map(async (asset) => {
      try {
        const loaded = await this.loadAsset(asset.id, asset.url, asset.type)
        results.set(asset.id, loaded)
      } catch (error) {
        console.error(`Failed to load asset ${asset.id}:`, error)
      }
    })

    await Promise.allSettled(promises)
    return results
  }

  /**
   * 获取资产
   */
  getAsset(id: string): any | undefined {
    return this.assets.get(id)
  }

  /**
   * 获取资产元数据
   */
  getMetadata(id: string): AssetMetadata | undefined {
    return this.metadata.get(id)
  }

  /**
   * 卸载资产
   */
  unloadAsset(id: string): boolean {
    const asset = this.assets.get(id)
    if (asset) {
      // 释放资源
      if (asset.dispose) {
        asset.dispose()
      }
      
      this.assets.delete(id)
      this.metadata.delete(id)
      this.cache.remove(id)
      
      return true
    }
    return false
  }

  /**
   * 预加载资产
   */
  async preloadAsset(id: string, url: string, type: AssetType): Promise<void> {
    if (this.streaming.isSupported()) {
      await this.streaming.preload(id, url, type)
    } else {
      await this.loadAsset(id, url, type)
    }
  }

  /**
   * 设置进度回调
   */
  setProgressCallback(callback: (progress: LoadProgress) => void): void {
    this.onProgress = callback
  }

  /**
   * 设置错误回调
   */
  setErrorCallback(callback: (error: Error, assetId: string) => void): void {
    this.onError = callback
  }

  /**
   * 获取所有资产
   */
  getAllAssets(): Map<string, any> {
    return new Map(this.assets)
  }

  /**
   * 获取加载统计
   */
  getLoadingStats(): {
    total: number
    loaded: number
    loading: number
    error: number
  } {
    let loaded = 0
    let loading = 0
    let error = 0

    for (const metadata of this.metadata.values()) {
      switch (metadata.status) {
        case AssetStatus.LOADED:
          loaded++
          break
        case AssetStatus.LOADING:
          loading++
          break
        case AssetStatus.ERROR:
          error++
          break
      }
    }

    return {
      total: this.metadata.size,
      loaded,
      loading,
      error
    }
  }

  /**
   * 清理未使用的资产
   */
  cleanup(): void {
    // 实现引用计数和自动清理逻辑
    console.log('Asset cleanup completed')
  }

  /**
   * 销毁资产管理器
   */
  async destroy(): Promise<void> {
    // 卸载所有资产
    for (const id of this.assets.keys()) {
      this.unloadAsset(id)
    }

    // 销毁子系统
    await this.streaming.destroy()
    await this.cache.destroy()

    this.assets.clear()
    this.metadata.clear()
    this.loadingPromises.clear()

    console.log('Asset manager destroyed')
  }

  /**
   * 执行实际加载
   */
  private async performLoad(id: string, url: string, type: AssetType): Promise<any> {
    const metadata = this.metadata.get(id)!
    metadata.status = AssetStatus.LOADING

    switch (type) {
      case AssetType.TEXTURE:
        return this.loadTexture(url)
      case AssetType.MODEL:
        return this.loadModel(url)
      case AssetType.AUDIO:
        return this.loadAudio(url)
      case AssetType.VIDEO:
        return this.loadVideo(url)
      default:
        throw new Error(`Unsupported asset type: ${type}`)
    }
  }

  /**
   * 加载纹理
   */
  private async loadTexture(url: string): Promise<THREE.Texture> {
    return new Promise((resolve, reject) => {
      this.textureLoader.load(
        url,
        (texture) => {
          texture.colorSpace = THREE.SRGBColorSpace
          resolve(texture)
        },
        (progress) => {
          if (this.onProgress) {
            this.onProgress({
              loaded: progress.loaded,
              total: progress.total,
              percentage: (progress.loaded / progress.total) * 100
            })
          }
        },
        reject
      )
    })
  }

  /**
   * 加载模型
   */
  private async loadModel(url: string): Promise<any> {
    return this.gltfLoader.load(url)
  }

  /**
   * 加载音频
   */
  private async loadAudio(url: string): Promise<AudioBuffer> {
    return new Promise((resolve, reject) => {
      this.audioLoader.load(url, resolve, undefined, reject)
    })
  }

  /**
   * 加载视频
   */
  private async loadVideo(url: string): Promise<HTMLVideoElement> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video')
      video.crossOrigin = 'anonymous'
      video.onloadeddata = () => resolve(video)
      video.onerror = reject
      video.src = url
    })
  }
}
