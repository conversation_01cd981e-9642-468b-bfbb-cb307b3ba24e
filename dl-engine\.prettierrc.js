/*
CPAL-1.0 License
*/

module.exports = {
  // 基础格式化选项
  semi: false,
  singleQuote: true,
  quoteProps: 'as-needed',
  trailingComma: 'none',
  bracketSpacing: true,
  bracketSameLine: false,
  arrowParens: 'avoid',
  
  // 缩进和换行
  tabWidth: 2,
  useTabs: false,
  printWidth: 120,
  endOfLine: 'lf',
  
  // 特定文件类型配置
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 80,
        trailingComma: 'none'
      }
    },
    {
      files: '*.md',
      options: {
        printWidth: 80,
        proseWrap: 'always'
      }
    },
    {
      files: '*.yml',
      options: {
        singleQuote: false
      }
    }
  ],
  
  // 插件配置
  plugins: [
    '@ianvs/prettier-plugin-sort-imports',
    'prettier-plugin-organize-imports'
  ],
  
  // 导入排序配置
  importOrder: [
    '^react$',
    '^react-dom$',
    '^@?\\w',
    '^@dl-engine/(.*)$',
    '^[./]'
  ],
  importOrderSeparation: true,
  importOrderSortSpecifiers: true
}
