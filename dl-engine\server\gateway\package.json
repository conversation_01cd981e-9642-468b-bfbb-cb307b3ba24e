{"name": "@dl-engine/server-gateway", "version": "1.0.0", "description": "DL-Engine API网关服务 - 路由、中间件、限流、负载均衡", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "build": "tsc", "build:watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "check-errors": "tsc --noEmit", "validate": "npm run lint && npm run check-errors && npm run test"}, "keywords": ["dl-engine", "gateway", "api-gateway", "microservices", "routing", "middleware", "rate-limiting", "load-balancing"], "author": {"name": "Digital Learning Engine Team", "email": "<EMAIL>"}, "license": "CPAL-1.0", "dependencies": {"@fastify/cors": "^10.0.1", "@fastify/helmet": "^12.0.1", "@fastify/rate-limit": "^10.1.1", "@nestjs/common": "^10.4.8", "@nestjs/core": "^10.4.8", "@nestjs/platform-fastify": "^10.4.8", "@nestjs/swagger": "^8.0.5", "fastify": "^5.1.0", "http-proxy-middleware": "^3.0.3", "ioredis": "^5.4.1", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22.11.0", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitest/coverage-istanbul": "2.1.1", "eslint": "9.5.0", "prettier": "3.0.2", "rimraf": "4.4.0", "ts-node-dev": "^2.0.0", "typescript": "5.6.3", "vitest": "2.1.1"}, "engines": {"node": ">= 22.11.0"}}