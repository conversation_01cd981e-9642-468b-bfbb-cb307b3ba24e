/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

/**
 * DL-Engine ECS 系统
 *
 * 实体组件系统架构，提供：
 * - 实体管理
 * - 组件系统
 * - 系统调度
 * - 网络同步
 */

// ECS 系统导出
export * from './world'
export * from './entities'
export * from './components'
export * from './systems'
export * from './networking'

// 主要类导出
export { ECSWorld } from './world'
export { EntityManager } from './entities'
export { ComponentManager } from './components'
export { SystemManager } from './systems'
export { NetworkingSystem } from './networking'
