# DL-Engine 核心引擎

这是 DL-Engine 的核心引擎模块，包含：

## 主要组件

- `core/` - 核心引擎系统
  - `rendering/` - 渲染系统 (Three.js集成)
  - `assets/` - 资产系统
  - `animation/` - 动画系统
  - `scene/` - 场景管理
  - `audio/` - 音频系统
  - `utils/` - 工具库

- `ecs/` - ECS系统
  - `entities/` - 实体管理
  - `components/` - 组件系统
  - `systems/` - 系统调度
  - `networking/` - 网络同步

- `physics/` - 物理引擎
  - `rapier/` - Rapier3D集成
  - `collision/` - 碰撞检测
  - `constraints/` - 约束系统
  - `queries/` - 空间查询
  - `networking/` - 网络物理

- `state/` - 状态管理
  - `store/` - 状态存储
  - `actions/` - 动作系统
  - `sync/` - 网络同步
  - `persistence/` - 持久化

- `xr/` - XR支持
  - `webxr/` - WebXR集成
  - `ui/` - 3D UI渲染
  - `interaction/` - 交互系统

- `ai/` - AI集成
  - `ollama/` - Ollama集成
  - `nlp/` - 自然语言处理
  - `analytics/` - 学习分析

## 技术栈

- TypeScript 5.6.3
- Three.js 0.176.0
- cannon-es (客户端物理)
- Rapier3D 0.11.2 (服务端物理)
- WebXR
- ECS架构 (bitECS/自研组合)
