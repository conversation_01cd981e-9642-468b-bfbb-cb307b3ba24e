/*
CPAL-1.0 License
*/

import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { TaskModule } from './task/task.module'
import { QueueModule } from './queue/queue.module'
import { HealthModule } from './health/health.module'

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    TaskModule,
    QueueModule,
    HealthModule
  ]
})
export class AppModule {}
