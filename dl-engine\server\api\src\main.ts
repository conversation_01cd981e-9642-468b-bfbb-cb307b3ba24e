/*
CPAL-1.0 License
*/

import { NestFactory } from '@nestjs/core'
import { AppModule } from './app.module'
import { Logger, ValidationPipe } from '@nestjs/common'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import { ConfigService } from '@nestjs/config'

async function bootstrap() {
  const logger = new Logger('ApiService')
  const app = await NestFactory.create(AppModule)

  // 获取配置服务
  const configService = app.get(ConfigService)

  // 启用 CORS
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*'),
    credentials: true
  })

  // 全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true
  }))

  // 设置全局前缀
  app.setGlobalPrefix('api/v1')

  // Swagger 文档配置
  const config = new DocumentBuilder()
    .setTitle('DL-Engine API')
    .setDescription('Digital Learning Engine API Documentation')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('projects', '项目管理')
    .addTag('scenes', '场景管理')
    .addTag('assets', '资产管理')
    .addTag('users', '用户管理')
    .build()

  const document = SwaggerModule.createDocument(app, config)
  SwaggerModule.setup('api/docs', app, document)

  const port = configService.get('PORT', 3032)
  await app.listen(port)

  logger.log(`🌐 API Service is running on: http://localhost:${port}`)
  logger.log(`📚 API Documentation: http://localhost:${port}/api/docs`)
}

bootstrap()
