/*
CPAL-1.0 License
*/

import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common'
import { CreateSceneDto, UpdateSceneDto, SceneQueryDto } from './scenes.controller'

/**
 * 场景接口
 */
export interface Scene {
  id: string
  name: string
  description?: string
  projectId: string
  template?: string
  settings: Record<string, any>
  sceneData: any
  createdAt: Date
  updatedAt: Date
  lastAccessedAt?: Date
  version: string
  isPublished: boolean
  publishedAt?: Date
  previewUrl?: string
}

/**
 * 场景版本接口
 */
export interface SceneVersion {
  id: string
  sceneId: string
  version: string
  sceneData: any
  notes?: string
  createdAt: Date
  createdBy: string
}

/**
 * 场景列表响应接口
 */
export interface SceneListResponse {
  scenes: Scene[]
  total: number
  page: number
  limit: number
  totalPages: number
}

@Injectable()
export class ScenesService {
  private scenes = new Map<string, Scene>()
  private sceneVersions = new Map<string, SceneVersion[]>() // sceneId -> versions
  private nextSceneId = 1

  constructor() {
    // 初始化一些测试场景
    this.initTestScenes()
  }

  /**
   * 初始化测试场景
   */
  private initTestScenes(): void {
    const testScene: Scene = {
      id: 'scene_001',
      name: '示例场景',
      description: '这是一个示例场景',
      projectId: 'project_001',
      template: 'basic',
      settings: {
        lighting: 'realistic',
        shadows: true,
        fog: false,
        skybox: 'default'
      },
      sceneData: {
        entities: [],
        lights: [
          {
            type: 'directional',
            position: [10, 10, 10],
            intensity: 1.0,
            color: '#ffffff'
          }
        ],
        camera: {
          position: [0, 5, 10],
          target: [0, 0, 0],
          fov: 75
        }
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      version: '1.0.0',
      isPublished: false
    }
    
    this.scenes.set(testScene.id, testScene)
    this.sceneVersions.set(testScene.id, [])
  }

  /**
   * 创建场景
   */
  async create(createSceneDto: CreateSceneDto, userId: string): Promise<Scene> {
    // 这里应该验证用户对项目的访问权限
    // 暂时跳过权限检查
    
    const id = `scene_${this.nextSceneId++}`
    
    const scene: Scene = {
      id,
      name: createSceneDto.name,
      description: createSceneDto.description,
      projectId: createSceneDto.projectId,
      template: createSceneDto.template || 'basic',
      settings: createSceneDto.settings || {
        lighting: 'realistic',
        shadows: true,
        fog: false,
        skybox: 'default'
      },
      sceneData: this.getTemplateSceneData(createSceneDto.template || 'basic'),
      createdAt: new Date(),
      updatedAt: new Date(),
      version: '1.0.0',
      isPublished: false
    }

    this.scenes.set(id, scene)
    this.sceneVersions.set(id, [])

    return scene
  }

  /**
   * 获取模板场景数据
   */
  private getTemplateSceneData(template: string): any {
    const templates = {
      basic: {
        entities: [],
        lights: [
          {
            type: 'directional',
            position: [10, 10, 10],
            intensity: 1.0,
            color: '#ffffff'
          }
        ],
        camera: {
          position: [0, 5, 10],
          target: [0, 0, 0],
          fov: 75
        }
      },
      classroom: {
        entities: [
          {
            type: 'mesh',
            geometry: 'box',
            material: 'standard',
            position: [0, 0, 0],
            scale: [10, 0.1, 10],
            name: 'floor'
          }
        ],
        lights: [
          {
            type: 'directional',
            position: [5, 10, 5],
            intensity: 0.8,
            color: '#ffffff'
          },
          {
            type: 'ambient',
            intensity: 0.3,
            color: '#404040'
          }
        ],
        camera: {
          position: [0, 2, 5],
          target: [0, 1, 0],
          fov: 75
        }
      },
      outdoor: {
        entities: [
          {
            type: 'terrain',
            size: [100, 100],
            position: [0, 0, 0],
            name: 'terrain'
          }
        ],
        lights: [
          {
            type: 'directional',
            position: [50, 50, 50],
            intensity: 1.2,
            color: '#fff8dc'
          }
        ],
        camera: {
          position: [0, 10, 20],
          target: [0, 0, 0],
          fov: 75
        }
      }
    }

    return templates[template] || templates.basic
  }

  /**
   * 获取场景列表
   */
  async findAll(query: SceneQueryDto, userId: string): Promise<SceneListResponse> {
    const { page = 1, limit = 10, search, sortBy = 'updatedAt', sortOrder = 'desc', projectId } = query

    let scenes = Array.from(this.scenes.values())

    // 项目过滤
    if (projectId) {
      scenes = scenes.filter(scene => scene.projectId === projectId)
    }

    // 这里应该添加权限过滤
    // 暂时返回所有场景

    // 搜索过滤
    if (search) {
      const searchLower = search.toLowerCase()
      scenes = scenes.filter(scene =>
        scene.name.toLowerCase().includes(searchLower) ||
        scene.description?.toLowerCase().includes(searchLower)
      )
    }

    // 排序
    scenes.sort((a, b) => {
      const aValue = a[sortBy]
      const bValue = b[sortBy]
      
      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1
      return 0
    })

    // 分页
    const total = scenes.length
    const totalPages = Math.ceil(total / limit)
    const startIndex = (page - 1) * limit
    const paginatedScenes = scenes.slice(startIndex, startIndex + limit)

    return {
      scenes: paginatedScenes,
      total,
      page,
      limit,
      totalPages
    }
  }

  /**
   * 获取单个场景
   */
  async findOne(id: string, userId: string): Promise<Scene> {
    const scene = this.scenes.get(id)
    
    if (!scene) {
      throw new NotFoundException('Scene not found')
    }

    // 这里应该检查访问权限
    // 暂时跳过权限检查

    // 更新最后访问时间
    scene.lastAccessedAt = new Date()

    return scene
  }

  /**
   * 更新场景
   */
  async update(id: string, updateSceneDto: UpdateSceneDto, userId: string): Promise<Scene> {
    const scene = this.scenes.get(id)
    
    if (!scene) {
      throw new NotFoundException('Scene not found')
    }

    // 这里应该检查编辑权限
    // 暂时跳过权限检查

    // 如果场景数据发生变化，创建新版本
    if (updateSceneDto.sceneData && JSON.stringify(updateSceneDto.sceneData) !== JSON.stringify(scene.sceneData)) {
      await this.createVersion(scene, userId)
    }

    // 更新场景
    const updatedScene = {
      ...scene,
      ...updateSceneDto,
      updatedAt: new Date()
    }

    this.scenes.set(id, updatedScene)
    return updatedScene
  }

  /**
   * 删除场景
   */
  async remove(id: string, userId: string): Promise<void> {
    const scene = this.scenes.get(id)
    
    if (!scene) {
      throw new NotFoundException('Scene not found')
    }

    // 这里应该检查删除权限
    // 暂时跳过权限检查

    this.scenes.delete(id)
    this.sceneVersions.delete(id)
  }

  /**
   * 复制场景
   */
  async duplicate(id: string, userId: string, name?: string, projectId?: string): Promise<Scene> {
    const originalScene = await this.findOne(id, userId)
    
    const duplicatedScene: Scene = {
      ...originalScene,
      id: `scene_${this.nextSceneId++}`,
      name: name || `${originalScene.name} (副本)`,
      projectId: projectId || originalScene.projectId,
      createdAt: new Date(),
      updatedAt: new Date(),
      version: '1.0.0',
      isPublished: false,
      publishedAt: undefined,
      previewUrl: undefined
    }

    this.scenes.set(duplicatedScene.id, duplicatedScene)
    this.sceneVersions.set(duplicatedScene.id, [])

    return duplicatedScene
  }

  /**
   * 导出场景
   */
  async exportScene(id: string, userId: string, format: 'gltf' | 'fbx' | 'obj'): Promise<{ downloadUrl: string }> {
    const scene = await this.findOne(id, userId)
    
    // 这里应该实现实际的导出逻辑
    // 暂时返回模拟的下载链接
    const downloadUrl = `${process.env.API_URL || 'http://localhost:3032'}/api/v1/scenes/${id}/download?format=${format}`
    
    return { downloadUrl }
  }

  /**
   * 导入场景
   */
  async importScene(importData: {
    projectId: string
    sceneData: any
    name?: string
    description?: string
  }, userId: string): Promise<Scene> {
    const createSceneDto: CreateSceneDto = {
      name: importData.name || '导入的场景',
      description: importData.description,
      projectId: importData.projectId
    }

    const scene = await this.create(createSceneDto, userId)
    
    // 更新场景数据
    scene.sceneData = importData.sceneData
    scene.updatedAt = new Date()
    
    this.scenes.set(scene.id, scene)
    
    return scene
  }

  /**
   * 生成场景预览
   */
  async generatePreview(id: string, userId: string, width: number, height: number): Promise<{ previewUrl: string }> {
    const scene = await this.findOne(id, userId)
    
    // 这里应该实现实际的预览生成逻辑
    // 暂时返回模拟的预览链接
    const previewUrl = `${process.env.API_URL || 'http://localhost:3032'}/api/v1/scenes/${id}/preview.png?w=${width}&h=${height}`
    
    // 更新场景的预览URL
    scene.previewUrl = previewUrl
    scene.updatedAt = new Date()
    
    return { previewUrl }
  }

  /**
   * 发布场景
   */
  async publishScene(id: string, userId: string, version?: string, notes?: string): Promise<Scene> {
    const scene = await this.findOne(id, userId)
    
    // 创建发布版本
    await this.createVersion(scene, userId, notes)
    
    // 更新场景状态
    scene.isPublished = true
    scene.publishedAt = new Date()
    scene.version = version || this.incrementVersion(scene.version)
    scene.updatedAt = new Date()
    
    this.scenes.set(id, scene)
    
    return scene
  }

  /**
   * 创建场景版本
   */
  private async createVersion(scene: Scene, userId: string, notes?: string): Promise<SceneVersion> {
    const versions = this.sceneVersions.get(scene.id) || []
    
    const version: SceneVersion = {
      id: `version_${Date.now()}`,
      sceneId: scene.id,
      version: scene.version,
      sceneData: JSON.parse(JSON.stringify(scene.sceneData)), // 深拷贝
      notes,
      createdAt: new Date(),
      createdBy: userId
    }
    
    versions.push(version)
    this.sceneVersions.set(scene.id, versions)
    
    return version
  }

  /**
   * 获取版本历史
   */
  async getVersionHistory(id: string, userId: string): Promise<SceneVersion[]> {
    await this.findOne(id, userId) // 验证访问权限
    return this.sceneVersions.get(id) || []
  }

  /**
   * 恢复到指定版本
   */
  async restoreVersion(id: string, version: string, userId: string): Promise<Scene> {
    const scene = await this.findOne(id, userId)
    const versions = this.sceneVersions.get(id) || []
    
    const targetVersion = versions.find(v => v.version === version)
    if (!targetVersion) {
      throw new NotFoundException('Version not found')
    }
    
    // 先创建当前版本的备份
    await this.createVersion(scene, userId, `恢复前的备份 (${scene.version})`)
    
    // 恢复场景数据
    scene.sceneData = JSON.parse(JSON.stringify(targetVersion.sceneData))
    scene.version = this.incrementVersion(scene.version)
    scene.updatedAt = new Date()
    
    this.scenes.set(id, scene)
    
    return scene
  }

  /**
   * 递增版本号
   */
  private incrementVersion(currentVersion: string): string {
    const parts = currentVersion.split('.')
    const patch = parseInt(parts[2] || '0') + 1
    return `${parts[0]}.${parts[1]}.${patch}`
  }
}
