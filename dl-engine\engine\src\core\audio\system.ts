/*
CPAL-1.0 License
*/

import * as THREE from 'three'
import { AudioConfig } from '../config'

/**
 * 音频系统
 */
export class AudioSystem {
  private config: AudioConfig
  private listener?: THREE.AudioListener
  private audioContext?: AudioContext
  private sounds = new Map<string, THREE.Audio>()

  constructor(config: AudioConfig) {
    this.config = config
  }

  /**
   * 初始化音频系统
   */
  async initialize(): Promise<void> {
    if (!this.config.enabled) return

    try {
      this.audioContext = this.config.audioContext || new AudioContext()
      this.listener = new THREE.AudioListener()
      
      console.log('Audio system initialized')
    } catch (error) {
      console.error('Failed to initialize audio system:', error)
      throw error
    }
  }

  /**
   * 创建音频
   */
  createAudio(id: string, buffer: AudioBuffer): THREE.Audio {
    if (!this.listener) {
      throw new Error('Audio system not initialized')
    }

    const audio = new THREE.Audio(this.listener)
    audio.setBuffer(buffer)
    audio.setVolume(this.config.masterVolume)
    
    this.sounds.set(id, audio)
    return audio
  }

  /**
   * 播放音频
   */
  play(id: string): void {
    const audio = this.sounds.get(id)
    if (audio && !audio.isPlaying) {
      audio.play()
    }
  }

  /**
   * 停止音频
   */
  stop(id: string): void {
    const audio = this.sounds.get(id)
    if (audio && audio.isPlaying) {
      audio.stop()
    }
  }

  /**
   * 设置主音量
   */
  setMasterVolume(volume: number): void {
    this.config.masterVolume = Math.max(0, Math.min(1, volume))
    
    for (const audio of this.sounds.values()) {
      audio.setVolume(this.config.masterVolume)
    }
  }

  /**
   * 获取音频监听器
   */
  getListener(): THREE.AudioListener | undefined {
    return this.listener
  }

  /**
   * 更新音频系统
   */
  update(deltaTime: number): void {
    // 音频系统更新逻辑
  }

  /**
   * 销毁音频系统
   */
  async destroy(): Promise<void> {
    // 停止所有音频
    for (const audio of this.sounds.values()) {
      if (audio.isPlaying) {
        audio.stop()
      }
      audio.disconnect()
    }
    
    this.sounds.clear()
    
    if (this.audioContext) {
      await this.audioContext.close()
      this.audioContext = undefined
    }
    
    this.listener = undefined
    
    console.log('Audio system destroyed')
  }
}
