/*
CPAL-1.0 License
*/

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus
} from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiQuery
} from '@nestjs/swagger'
import { ProjectsService } from './projects.service'
import { JwtAuthGuard } from '../auth/jwt-auth.guard'

/**
 * 创建项目DTO
 */
export class CreateProjectDto {
  name: string
  description?: string
  template?: string
  isPublic?: boolean
}

/**
 * 更新项目DTO
 */
export class UpdateProjectDto {
  name?: string
  description?: string
  isPublic?: boolean
  settings?: Record<string, any>
}

/**
 * 项目查询DTO
 */
export class ProjectQueryDto {
  page?: number = 1
  limit?: number = 10
  search?: string
  sortBy?: 'name' | 'createdAt' | 'updatedAt' = 'updatedAt'
  sortOrder?: 'asc' | 'desc' = 'desc'
  isPublic?: boolean
}

@ApiTags('projects')
@Controller('projects')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ProjectsController {
  constructor(private readonly projectsService: ProjectsService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '创建新项目' })
  @ApiBody({ type: CreateProjectDto })
  @ApiResponse({ status: 201, description: '项目创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 401, description: '未授权' })
  async createProject(
    @Body() createProjectDto: CreateProjectDto,
    @Request() req: any
  ) {
    return this.projectsService.create(createProjectDto, req.user.sub)
  }

  @Get()
  @ApiOperation({ summary: '获取项目列表' })
  @ApiQuery({ type: ProjectQueryDto })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getProjects(
    @Query() query: ProjectQueryDto,
    @Request() req: any
  ) {
    return this.projectsService.findAll(query, req.user.sub)
  }

  @Get(':id')
  @ApiOperation({ summary: '获取项目详情' })
  @ApiParam({ name: 'id', description: '项目ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '项目不存在' })
  async getProject(
    @Param('id') id: string,
    @Request() req: any
  ) {
    return this.projectsService.findOne(id, req.user.sub)
  }

  @Put(':id')
  @ApiOperation({ summary: '更新项目' })
  @ApiParam({ name: 'id', description: '项目ID' })
  @ApiBody({ type: UpdateProjectDto })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '项目不存在' })
  @ApiResponse({ status: 403, description: '无权限' })
  async updateProject(
    @Param('id') id: string,
    @Body() updateProjectDto: UpdateProjectDto,
    @Request() req: any
  ) {
    return this.projectsService.update(id, updateProjectDto, req.user.sub)
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除项目' })
  @ApiParam({ name: 'id', description: '项目ID' })
  @ApiResponse({ status: 204, description: '删除成功' })
  @ApiResponse({ status: 404, description: '项目不存在' })
  @ApiResponse({ status: 403, description: '无权限' })
  async deleteProject(
    @Param('id') id: string,
    @Request() req: any
  ) {
    return this.projectsService.remove(id, req.user.sub)
  }

  @Post(':id/duplicate')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '复制项目' })
  @ApiParam({ name: 'id', description: '项目ID' })
  @ApiResponse({ status: 201, description: '复制成功' })
  @ApiResponse({ status: 404, description: '项目不存在' })
  async duplicateProject(
    @Param('id') id: string,
    @Request() req: any
  ) {
    return this.projectsService.duplicate(id, req.user.sub)
  }

  @Post(':id/share')
  @ApiOperation({ summary: '分享项目' })
  @ApiParam({ name: 'id', description: '项目ID' })
  @ApiResponse({ status: 200, description: '分享链接生成成功' })
  async shareProject(
    @Param('id') id: string,
    @Request() req: any
  ) {
    return this.projectsService.generateShareLink(id, req.user.sub)
  }

  @Get(':id/collaborators')
  @ApiOperation({ summary: '获取项目协作者' })
  @ApiParam({ name: 'id', description: '项目ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getCollaborators(
    @Param('id') id: string,
    @Request() req: any
  ) {
    return this.projectsService.getCollaborators(id, req.user.sub)
  }

  @Post(':id/collaborators')
  @ApiOperation({ summary: '添加协作者' })
  @ApiParam({ name: 'id', description: '项目ID' })
  @ApiResponse({ status: 201, description: '添加成功' })
  async addCollaborator(
    @Param('id') id: string,
    @Body() body: { userId: string; role: 'viewer' | 'editor' | 'admin' },
    @Request() req: any
  ) {
    return this.projectsService.addCollaborator(id, body.userId, body.role, req.user.sub)
  }

  @Delete(':id/collaborators/:userId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '移除协作者' })
  @ApiParam({ name: 'id', description: '项目ID' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiResponse({ status: 204, description: '移除成功' })
  async removeCollaborator(
    @Param('id') id: string,
    @Param('userId') userId: string,
    @Request() req: any
  ) {
    return this.projectsService.removeCollaborator(id, userId, req.user.sub)
  }
}
