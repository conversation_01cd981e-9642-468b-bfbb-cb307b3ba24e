/*
CPAL-1.0 License
*/

import * as THREE from 'three'

/**
 * 场景管理器
 */
export class SceneManager {
  private scenes = new Map<string, THREE.Scene>()
  private activeScene?: THREE.Scene
  private activeSceneId?: string
  private renderer?: THREE.WebGLRenderer

  constructor() {
    // 创建默认场景
    this.createScene('default')
    this.setActiveScene('default')
  }

  /**
   * 创建场景
   */
  createScene(id: string): THREE.Scene {
    const scene = new THREE.Scene()
    scene.name = id
    this.scenes.set(id, scene)
    return scene
  }

  /**
   * 获取场景
   */
  getScene(id: string): THREE.Scene | undefined {
    return this.scenes.get(id)
  }

  /**
   * 设置活动场景
   */
  setActiveScene(id: string): boolean {
    const scene = this.scenes.get(id)
    if (scene) {
      this.activeScene = scene
      this.activeSceneId = id
      return true
    }
    return false
  }

  /**
   * 获取活动场景
   */
  getActiveScene(): THREE.Scene | undefined {
    return this.activeScene
  }

  /**
   * 设置渲染器
   */
  setRenderer(renderer: THREE.WebGLRenderer): void {
    this.renderer = renderer
  }

  /**
   * 删除场景
   */
  deleteScene(id: string): boolean {
    if (this.activeSceneId === id) {
      this.activeScene = undefined
      this.activeSceneId = undefined
    }
    return this.scenes.delete(id)
  }

  /**
   * 获取所有场景
   */
  getAllScenes(): Map<string, THREE.Scene> {
    return new Map(this.scenes)
  }
}
