/*
CPAL-1.0 License
*/

import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common'
import { JwtService } from '@nestjs/jwt'
import * as bcrypt from 'bcrypt'

/**
 * 登录DTO
 */
export interface LoginDto {
  phone: string
  password?: string
  verificationCode?: string
}

/**
 * 注册DTO
 */
export interface RegisterDto {
  phone: string
  password: string
  verificationCode: string
  username?: string
}

/**
 * 用户信息接口
 */
export interface User {
  id: string
  phone: string
  username: string
  email?: string
  avatar?: string
  createdAt: Date
  lastLoginAt?: Date
}

/**
 * 认证响应接口
 */
export interface AuthResponse {
  access_token: string
  refresh_token: string
  user: User
  expires_in: number
}

@Injectable()
export class AuthService {
  private users = new Map<string, User>() // 临时存储，实际应使用数据库
  private verificationCodes = new Map<string, { code: string; expires: Date }>()
  private refreshTokens = new Map<string, { userId: string; expires: Date }>()

  constructor(private readonly jwtService: JwtService) {
    // 初始化一些测试用户
    this.initTestUsers()
  }

  /**
   * 初始化测试用户
   */
  private async initTestUsers(): Promise<void> {
    const testUser: User = {
      id: 'user_001',
      phone: '13800138000',
      username: 'testuser',
      email: '<EMAIL>',
      createdAt: new Date(),
      lastLoginAt: new Date()
    }
    this.users.set(testUser.phone, testUser)
  }

  /**
   * 发送验证码
   */
  async sendVerificationCode(phone: string): Promise<{ message: string }> {
    // 验证手机号格式
    if (!this.isValidPhone(phone)) {
      throw new BadRequestException('Invalid phone number format')
    }

    // 生成6位验证码
    const code = Math.random().toString().slice(2, 8)
    const expires = new Date(Date.now() + 5 * 60 * 1000) // 5分钟过期

    this.verificationCodes.set(phone, { code, expires })

    // 实际应用中这里应该调用短信服务
    console.log(`Verification code for ${phone}: ${code}`)

    return { message: 'Verification code sent successfully' }
  }

  /**
   * 用户登录
   */
  async login(loginDto: LoginDto): Promise<AuthResponse> {
    const { phone, password, verificationCode } = loginDto

    // 验证手机号格式
    if (!this.isValidPhone(phone)) {
      throw new BadRequestException('Invalid phone number format')
    }

    const user = this.users.get(phone)

    // 如果用户不存在且提供了验证码，则自动注册
    if (!user && verificationCode) {
      return this.autoRegister(phone, verificationCode)
    }

    if (!user) {
      throw new UnauthorizedException('User not found')
    }

    // 验证码登录
    if (verificationCode) {
      if (!this.verifyCode(phone, verificationCode)) {
        throw new UnauthorizedException('Invalid verification code')
      }
    }
    // 密码登录（如果实现了密码功能）
    else if (password) {
      // 这里应该验证密码
      throw new BadRequestException('Password login not implemented')
    } else {
      throw new BadRequestException('Either password or verification code is required')
    }

    // 更新最后登录时间
    user.lastLoginAt = new Date()

    return this.generateAuthResponse(user)
  }

  /**
   * 用户注册
   */
  async register(registerDto: RegisterDto): Promise<AuthResponse> {
    const { phone, password, verificationCode, username } = registerDto

    // 验证手机号格式
    if (!this.isValidPhone(phone)) {
      throw new BadRequestException('Invalid phone number format')
    }

    // 检查用户是否已存在
    if (this.users.has(phone)) {
      throw new BadRequestException('User already exists')
    }

    // 验证验证码
    if (!this.verifyCode(phone, verificationCode)) {
      throw new UnauthorizedException('Invalid verification code')
    }

    // 创建新用户
    const user: User = {
      id: `user_${Date.now()}`,
      phone,
      username: username || `user_${phone.slice(-4)}`,
      createdAt: new Date(),
      lastLoginAt: new Date()
    }

    this.users.set(phone, user)

    return this.generateAuthResponse(user)
  }

  /**
   * 自动注册（验证码登录时用户不存在）
   */
  private async autoRegister(phone: string, verificationCode: string): Promise<AuthResponse> {
    if (!this.verifyCode(phone, verificationCode)) {
      throw new UnauthorizedException('Invalid verification code')
    }

    const user: User = {
      id: `user_${Date.now()}`,
      phone,
      username: `user_${phone.slice(-4)}`,
      createdAt: new Date(),
      lastLoginAt: new Date()
    }

    this.users.set(phone, user)

    return this.generateAuthResponse(user)
  }

  /**
   * 获取用户信息
   */
  async getProfile(userId: string): Promise<User> {
    const user = Array.from(this.users.values()).find(u => u.id === userId)
    if (!user) {
      throw new UnauthorizedException('User not found')
    }
    return user
  }

  /**
   * 刷新令牌
   */
  async refresh(refreshToken: string): Promise<{ access_token: string }> {
    const tokenData = this.refreshTokens.get(refreshToken)

    if (!tokenData || tokenData.expires < new Date()) {
      throw new UnauthorizedException('Invalid or expired refresh token')
    }

    const user = Array.from(this.users.values()).find(u => u.id === tokenData.userId)
    if (!user) {
      throw new UnauthorizedException('User not found')
    }

    const payload = { sub: user.id, phone: user.phone, username: user.username }
    const access_token = this.jwtService.sign(payload, { expiresIn: '1h' })

    return { access_token }
  }
  /**
   * 验证手机号格式
   */
  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }

  /**
   * 验证验证码
   */
  private verifyCode(phone: string, code: string): boolean {
    const storedCode = this.verificationCodes.get(phone)

    if (!storedCode) {
      return false
    }

    if (storedCode.expires < new Date()) {
      this.verificationCodes.delete(phone)
      return false
    }

    if (storedCode.code !== code) {
      return false
    }

    // 验证成功后删除验证码
    this.verificationCodes.delete(phone)
    return true
  }

  /**
   * 生成认证响应
   */
  private generateAuthResponse(user: User): AuthResponse {
    const payload = { sub: user.id, phone: user.phone, username: user.username }
    const access_token = this.jwtService.sign(payload, { expiresIn: '1h' })
    const refresh_token = this.generateRefreshToken(user.id)

    return {
      access_token,
      refresh_token,
      user,
      expires_in: 3600 // 1小时
    }
  }

  /**
   * 生成刷新令牌
   */
  private generateRefreshToken(userId: string): string {
    const token = Math.random().toString(36).substring(2, 15) +
                  Math.random().toString(36).substring(2, 15)
    const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天过期

    this.refreshTokens.set(token, { userId, expires })
    return token
  }

  /**
   * 验证JWT令牌
   */
  async validateToken(token: string): Promise<User | null> {
    try {
      const payload = this.jwtService.verify(token)
      const user = Array.from(this.users.values()).find(u => u.id === payload.sub)
      return user || null
    } catch (error) {
      return null
    }
  }

  /**
   * 登出
   */
  async logout(refreshToken: string): Promise<{ message: string }> {
    this.refreshTokens.delete(refreshToken)
    return { message: 'Logged out successfully' }
  }
}
