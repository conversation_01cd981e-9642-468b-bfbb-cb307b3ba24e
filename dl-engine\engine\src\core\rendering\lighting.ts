/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import * as THREE from 'three'

/**
 * 光照类型枚举
 */
export enum LightType {
  AMBIENT = 'ambient',
  DIRECTIONAL = 'directional',
  POINT = 'point',
  SPOT = 'spot',
  HEMISPHERE = 'hemisphere',
  RECT_AREA = 'rectArea'
}

/**
 * 光照配置接口
 */
export interface LightConfig {
  type: LightType
  color?: THREE.ColorRepresentation
  intensity?: number
  position?: THREE.Vector3
  target?: THREE.Vector3
  distance?: number
  decay?: number
  angle?: number
  penumbra?: number
  castShadow?: boolean
  shadowMapSize?: number
  shadowBias?: number
  shadowRadius?: number
}

/**
 * 光照系统
 * 
 * 管理场景中的所有光源
 */
export class LightingSystem {
  private lights = new Map<string, THREE.Light>()
  private ambientLight?: THREE.AmbientLight
  private hemisphereLight?: THREE.HemisphereLight

  /**
   * 初始化光照系统
   */
  async initialize(): Promise<void> {
    // 创建默认环境光
    this.createDefaultLighting()
    
    console.log('Lighting system initialized')
  }

  /**
   * 创建光源
   */
  createLight(id: string, config: LightConfig): THREE.Light {
    let light: THREE.Light

    switch (config.type) {
      case LightType.AMBIENT:
        light = this.createAmbientLight(config)
        break
      case LightType.DIRECTIONAL:
        light = this.createDirectionalLight(config)
        break
      case LightType.POINT:
        light = this.createPointLight(config)
        break
      case LightType.SPOT:
        light = this.createSpotLight(config)
        break
      case LightType.HEMISPHERE:
        light = this.createHemisphereLight(config)
        break
      case LightType.RECT_AREA:
        light = this.createRectAreaLight(config)
        break
      default:
        light = this.createDirectionalLight(config)
    }

    // 设置阴影
    if (config.castShadow && 'castShadow' in light) {
      this.configureShadow(light as any, config)
    }

    this.lights.set(id, light)
    return light
  }

  /**
   * 获取光源
   */
  getLight(id: string): THREE.Light | undefined {
    return this.lights.get(id)
  }

  /**
   * 删除光源
   */
  deleteLight(id: string): boolean {
    const light = this.lights.get(id)
    if (light) {
      if (light.parent) {
        light.parent.remove(light)
      }
      light.dispose()
      return this.lights.delete(id)
    }
    return false
  }

  /**
   * 更新光照系统
   */
  update(scene: THREE.Scene): void {
    // 更新动态光源
    for (const light of this.lights.values()) {
      if ('target' in light && light.target) {
        light.target.updateMatrixWorld()
      }
    }
  }

  /**
   * 设置环境光
   */
  setAmbientLight(color: THREE.ColorRepresentation, intensity: number): void {
    if (this.ambientLight) {
      this.ambientLight.color.set(color)
      this.ambientLight.intensity = intensity
    } else {
      this.ambientLight = new THREE.AmbientLight(color, intensity)
    }
  }

  /**
   * 获取环境光
   */
  getAmbientLight(): THREE.AmbientLight | undefined {
    return this.ambientLight
  }

  /**
   * 设置半球光
   */
  setHemisphereLight(
    skyColor: THREE.ColorRepresentation,
    groundColor: THREE.ColorRepresentation,
    intensity: number
  ): void {
    if (this.hemisphereLight) {
      this.hemisphereLight.color.set(skyColor)
      this.hemisphereLight.groundColor.set(groundColor)
      this.hemisphereLight.intensity = intensity
    } else {
      this.hemisphereLight = new THREE.HemisphereLight(skyColor, groundColor, intensity)
    }
  }

  /**
   * 获取半球光
   */
  getHemisphereLight(): THREE.HemisphereLight | undefined {
    return this.hemisphereLight
  }

  /**
   * 获取所有光源
   */
  getAllLights(): Map<string, THREE.Light> {
    return new Map(this.lights)
  }

  /**
   * 销毁光照系统
   */
  async destroy(): Promise<void> {
    // 销毁所有光源
    for (const light of this.lights.values()) {
      light.dispose()
    }
    this.lights.clear()

    if (this.ambientLight) {
      this.ambientLight.dispose()
      this.ambientLight = undefined
    }

    if (this.hemisphereLight) {
      this.hemisphereLight.dispose()
      this.hemisphereLight = undefined
    }

    console.log('Lighting system destroyed')
  }

  /**
   * 创建默认光照
   */
  private createDefaultLighting(): void {
    // 默认环境光
    this.setAmbientLight(0x404040, 0.4)

    // 默认半球光
    this.setHemisphereLight(0x87CEEB, 0x8B4513, 0.6)

    // 默认方向光
    this.createLight('default-directional', {
      type: LightType.DIRECTIONAL,
      color: 0xffffff,
      intensity: 1.0,
      position: new THREE.Vector3(10, 10, 5),
      castShadow: true,
      shadowMapSize: 2048
    })
  }

  /**
   * 创建环境光
   */
  private createAmbientLight(config: LightConfig): THREE.AmbientLight {
    return new THREE.AmbientLight(
      config.color || 0xffffff,
      config.intensity || 0.5
    )
  }

  /**
   * 创建方向光
   */
  private createDirectionalLight(config: LightConfig): THREE.DirectionalLight {
    const light = new THREE.DirectionalLight(
      config.color || 0xffffff,
      config.intensity || 1.0
    )

    if (config.position) {
      light.position.copy(config.position)
    }

    if (config.target) {
      light.target.position.copy(config.target)
    }

    return light
  }

  /**
   * 创建点光源
   */
  private createPointLight(config: LightConfig): THREE.PointLight {
    const light = new THREE.PointLight(
      config.color || 0xffffff,
      config.intensity || 1.0,
      config.distance || 0,
      config.decay || 2
    )

    if (config.position) {
      light.position.copy(config.position)
    }

    return light
  }

  /**
   * 创建聚光灯
   */
  private createSpotLight(config: LightConfig): THREE.SpotLight {
    const light = new THREE.SpotLight(
      config.color || 0xffffff,
      config.intensity || 1.0,
      config.distance || 0,
      config.angle || Math.PI / 3,
      config.penumbra || 0,
      config.decay || 2
    )

    if (config.position) {
      light.position.copy(config.position)
    }

    if (config.target) {
      light.target.position.copy(config.target)
    }

    return light
  }

  /**
   * 创建半球光
   */
  private createHemisphereLight(config: LightConfig): THREE.HemisphereLight {
    return new THREE.HemisphereLight(
      config.color || 0x87CEEB,
      0x8B4513, // 地面颜色
      config.intensity || 0.6
    )
  }

  /**
   * 创建矩形区域光
   */
  private createRectAreaLight(config: LightConfig): THREE.RectAreaLight {
    const light = new THREE.RectAreaLight(
      config.color || 0xffffff,
      config.intensity || 1.0,
      10, // width
      10  // height
    )

    if (config.position) {
      light.position.copy(config.position)
    }

    return light
  }

  /**
   * 配置阴影
   */
  private configureShadow(
    light: THREE.DirectionalLight | THREE.SpotLight | THREE.PointLight,
    config: LightConfig
  ): void {
    light.castShadow = true

    if (light.shadow) {
      const mapSize = config.shadowMapSize || 1024
      light.shadow.mapSize.width = mapSize
      light.shadow.mapSize.height = mapSize
      light.shadow.bias = config.shadowBias || -0.0001
      light.shadow.radius = config.shadowRadius || 1

      // 设置阴影相机参数
      if (light instanceof THREE.DirectionalLight) {
        const camera = light.shadow.camera as THREE.OrthographicCamera
        camera.left = -50
        camera.right = 50
        camera.top = 50
        camera.bottom = -50
        camera.near = 0.1
        camera.far = 200
      }
    }
  }
}
