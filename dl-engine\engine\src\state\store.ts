/*
CPAL-1.0 License
*/

/**
 * 状态存储
 */
export class StateStore {
  private state = new Map<string, any>()
  private subscribers = new Map<string, Set<(value: any) => void>>()

  async initialize(): Promise<void> {
    console.log('State store initialized')
  }

  getState<T>(path: string): T | undefined {
    return this.state.get(path)
  }

  setState<T>(path: string, value: T): void {
    this.state.set(path, value)
    this.notifySubscribers(path, value)
  }

  subscribe(path: string, callback: (value: any) => void): () => void {
    if (!this.subscribers.has(path)) {
      this.subscribers.set(path, new Set())
    }
    this.subscribers.get(path)!.add(callback)

    return () => {
      this.subscribers.get(path)?.delete(callback)
    }
  }

  getFullState(): Record<string, any> {
    return Object.fromEntries(this.state)
  }

  setFullState(state: Record<string, any>): void {
    this.state.clear()
    for (const [key, value] of Object.entries(state)) {
      this.setState(key, value)
    }
  }

  reset(): void {
    this.state.clear()
    this.subscribers.clear()
  }

  getSnapshot(): any {
    return this.getFullState()
  }

  restoreSnapshot(snapshot: any): void {
    this.setFullState(snapshot)
  }

  private notifySubscribers(path: string, value: any): void {
    const pathSubscribers = this.subscribers.get(path)
    if (pathSubscribers) {
      for (const callback of pathSubscribers) {
        callback(value)
      }
    }
  }

  async destroy(): Promise<void> {
    this.reset()
    console.log('State store destroyed')
  }
}
