# DL-Engine Redis 配置文件

# 基础配置
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 60

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

appendonly yes
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 日志配置
loglevel notice
logfile ""

# 安全配置
# requirepass dl_engine_redis_2024

# 客户端配置
maxclients 10000

# 网络配置
tcp-backlog 511

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 数据库数量
databases 16

# 键空间通知
notify-keyspace-events Ex

# 集群配置（开发环境暂时禁用）
# cluster-enabled no
