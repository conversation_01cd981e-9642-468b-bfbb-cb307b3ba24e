/*
CPAL-1.0 License
*/

/* DL-Engine 编辑器基础样式 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 禁用文本选择（在编辑器UI中） */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 3D视口样式 */
.viewport-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.viewport-canvas {
  display: block;
  width: 100%;
  height: 100%;
}

/* 编辑器布局样式 */
.editor-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.editor-header {
  flex-shrink: 0;
}

.editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-sidebar {
  flex-shrink: 0;
  border-right: 1px solid #d9d9d9;
}

.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.editor-footer {
  flex-shrink: 0;
  border-top: 1px solid #d9d9d9;
}
