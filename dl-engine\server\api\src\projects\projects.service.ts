/*
CPAL-1.0 License
*/

import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common'
import { CreateProjectDto, UpdateProjectDto, ProjectQueryDto } from './projects.controller'

/**
 * 项目接口
 */
export interface Project {
  id: string
  name: string
  description?: string
  ownerId: string
  isPublic: boolean
  template?: string
  settings: Record<string, any>
  createdAt: Date
  updatedAt: Date
  lastAccessedAt?: Date
  shareToken?: string
}

/**
 * 协作者接口
 */
export interface Collaborator {
  userId: string
  projectId: string
  role: 'viewer' | 'editor' | 'admin'
  addedAt: Date
  addedBy: string
}

/**
 * 项目列表响应接口
 */
export interface ProjectListResponse {
  projects: Project[]
  total: number
  page: number
  limit: number
  totalPages: number
}

@Injectable()
export class ProjectsService {
  private projects = new Map<string, Project>()
  private collaborators = new Map<string, Collaborator[]>() // projectId -> collaborators
  private nextProjectId = 1

  constructor() {
    // 初始化一些测试项目
    this.initTestProjects()
  }

  /**
   * 初始化测试项目
   */
  private initTestProjects(): void {
    const testProject: Project = {
      id: 'project_001',
      name: '示例项目',
      description: '这是一个示例项目',
      ownerId: 'user_001',
      isPublic: true,
      template: 'basic',
      settings: {
        physics: true,
        lighting: 'realistic',
        quality: 'high'
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    this.projects.set(testProject.id, testProject)
    this.collaborators.set(testProject.id, [])
  }

  /**
   * 创建项目
   */
  async create(createProjectDto: CreateProjectDto, userId: string): Promise<Project> {
    const id = `project_${this.nextProjectId++}`
    
    const project: Project = {
      id,
      name: createProjectDto.name,
      description: createProjectDto.description,
      ownerId: userId,
      isPublic: createProjectDto.isPublic || false,
      template: createProjectDto.template || 'basic',
      settings: {
        physics: true,
        lighting: 'realistic',
        quality: 'medium'
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }

    this.projects.set(id, project)
    this.collaborators.set(id, [])

    return project
  }

  /**
   * 获取项目列表
   */
  async findAll(query: ProjectQueryDto, userId: string): Promise<ProjectListResponse> {
    const { page = 1, limit = 10, search, sortBy = 'updatedAt', sortOrder = 'desc', isPublic } = query

    let projects = Array.from(this.projects.values())

    // 过滤用户可访问的项目
    projects = projects.filter(project => 
      project.ownerId === userId || 
      project.isPublic || 
      this.hasProjectAccess(project.id, userId)
    )

    // 搜索过滤
    if (search) {
      const searchLower = search.toLowerCase()
      projects = projects.filter(project =>
        project.name.toLowerCase().includes(searchLower) ||
        project.description?.toLowerCase().includes(searchLower)
      )
    }

    // 公开性过滤
    if (typeof isPublic === 'boolean') {
      projects = projects.filter(project => project.isPublic === isPublic)
    }

    // 排序
    projects.sort((a, b) => {
      const aValue = a[sortBy]
      const bValue = b[sortBy]
      
      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1
      return 0
    })

    // 分页
    const total = projects.length
    const totalPages = Math.ceil(total / limit)
    const startIndex = (page - 1) * limit
    const paginatedProjects = projects.slice(startIndex, startIndex + limit)

    return {
      projects: paginatedProjects,
      total,
      page,
      limit,
      totalPages
    }
  }

  /**
   * 获取单个项目
   */
  async findOne(id: string, userId: string): Promise<Project> {
    const project = this.projects.get(id)
    
    if (!project) {
      throw new NotFoundException('Project not found')
    }

    // 检查访问权限
    if (!this.canAccessProject(project, userId)) {
      throw new ForbiddenException('Access denied')
    }

    // 更新最后访问时间
    project.lastAccessedAt = new Date()

    return project
  }

  /**
   * 更新项目
   */
  async update(id: string, updateProjectDto: UpdateProjectDto, userId: string): Promise<Project> {
    const project = this.projects.get(id)
    
    if (!project) {
      throw new NotFoundException('Project not found')
    }

    // 检查编辑权限
    if (!this.canEditProject(project, userId)) {
      throw new ForbiddenException('Edit permission denied')
    }

    // 更新项目
    const updatedProject = {
      ...project,
      ...updateProjectDto,
      updatedAt: new Date()
    }

    this.projects.set(id, updatedProject)
    return updatedProject
  }

  /**
   * 删除项目
   */
  async remove(id: string, userId: string): Promise<void> {
    const project = this.projects.get(id)
    
    if (!project) {
      throw new NotFoundException('Project not found')
    }

    // 只有项目所有者可以删除
    if (project.ownerId !== userId) {
      throw new ForbiddenException('Only project owner can delete the project')
    }

    this.projects.delete(id)
    this.collaborators.delete(id)
  }

  /**
   * 复制项目
   */
  async duplicate(id: string, userId: string): Promise<Project> {
    const originalProject = await this.findOne(id, userId)
    
    const duplicatedProject: Project = {
      ...originalProject,
      id: `project_${this.nextProjectId++}`,
      name: `${originalProject.name} (副本)`,
      ownerId: userId,
      isPublic: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      shareToken: undefined
    }

    this.projects.set(duplicatedProject.id, duplicatedProject)
    this.collaborators.set(duplicatedProject.id, [])

    return duplicatedProject
  }

  /**
   * 生成分享链接
   */
  async generateShareLink(id: string, userId: string): Promise<{ shareUrl: string }> {
    const project = await this.findOne(id, userId)
    
    if (!this.canEditProject(project, userId)) {
      throw new ForbiddenException('Share permission denied')
    }

    const shareToken = Math.random().toString(36).substring(2, 15)
    project.shareToken = shareToken
    project.updatedAt = new Date()

    const shareUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/projects/shared/${shareToken}`
    
    return { shareUrl }
  }

  /**
   * 检查项目访问权限
   */
  private canAccessProject(project: Project, userId: string): boolean {
    return project.ownerId === userId || 
           project.isPublic || 
           this.hasProjectAccess(project.id, userId)
  }

  /**
   * 检查项目编辑权限
   */
  private canEditProject(project: Project, userId: string): boolean {
    if (project.ownerId === userId) return true
    
    const collaborator = this.getCollaborator(project.id, userId)
    return collaborator?.role === 'editor' || collaborator?.role === 'admin'
  }

  /**
   * 检查用户是否有项目访问权限
   */
  private hasProjectAccess(projectId: string, userId: string): boolean {
    return this.getCollaborator(projectId, userId) !== undefined
  }

  /**
   * 获取协作者信息
   */
  private getCollaborator(projectId: string, userId: string): Collaborator | undefined {
    const projectCollaborators = this.collaborators.get(projectId) || []
    return projectCollaborators.find(c => c.userId === userId)
  }

  /**
   * 获取项目协作者
   */
  async getCollaborators(projectId: string, userId: string): Promise<Collaborator[]> {
    const project = await this.findOne(projectId, userId)
    return this.collaborators.get(projectId) || []
  }

  /**
   * 添加协作者
   */
  async addCollaborator(
    projectId: string, 
    collaboratorUserId: string, 
    role: 'viewer' | 'editor' | 'admin',
    userId: string
  ): Promise<Collaborator> {
    const project = await this.findOne(projectId, userId)
    
    // 只有项目所有者和管理员可以添加协作者
    if (project.ownerId !== userId && this.getCollaborator(projectId, userId)?.role !== 'admin') {
      throw new ForbiddenException('Permission denied')
    }

    const projectCollaborators = this.collaborators.get(projectId) || []
    
    // 检查是否已经是协作者
    const existingCollaborator = projectCollaborators.find(c => c.userId === collaboratorUserId)
    if (existingCollaborator) {
      throw new BadRequestException('User is already a collaborator')
    }

    const collaborator: Collaborator = {
      userId: collaboratorUserId,
      projectId,
      role,
      addedAt: new Date(),
      addedBy: userId
    }

    projectCollaborators.push(collaborator)
    this.collaborators.set(projectId, projectCollaborators)

    return collaborator
  }

  /**
   * 移除协作者
   */
  async removeCollaborator(projectId: string, collaboratorUserId: string, userId: string): Promise<void> {
    const project = await this.findOne(projectId, userId)
    
    // 只有项目所有者和管理员可以移除协作者
    if (project.ownerId !== userId && this.getCollaborator(projectId, userId)?.role !== 'admin') {
      throw new ForbiddenException('Permission denied')
    }

    const projectCollaborators = this.collaborators.get(projectId) || []
    const updatedCollaborators = projectCollaborators.filter(c => c.userId !== collaboratorUserId)
    
    this.collaborators.set(projectId, updatedCollaborators)
  }
}
