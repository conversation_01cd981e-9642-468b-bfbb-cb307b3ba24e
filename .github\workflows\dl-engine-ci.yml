name: DL-Engine CI/CD

on:
  push:
    branches: [ main, dev ]
    paths:
      - 'dl-engine/**'
      - '.github/workflows/dl-engine-ci.yml'
  pull_request:
    branches: [ main, dev ]
    paths:
      - 'dl-engine/**'

env:
  NODE_VERSION: '22.11.0'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 代码质量检查
  lint-and-format:
    name: 代码质量检查
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Lint DL-Engine modules
        run: |
          cd dl-engine
          npm run lint

      - name: Check TypeScript
        run: |
          cd dl-engine
          npm run check-errors

      - name: Format check
        run: |
          cd dl-engine
          npm run format:check

  # 单元测试
  test:
    name: 单元测试
    runs-on: ubuntu-latest
    strategy:
      matrix:
        module: [engine, editor]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests for ${{ matrix.module }}
        run: |
          cd dl-engine/${{ matrix.module }}
          npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./dl-engine/${{ matrix.module }}/coverage/lcov.info
          flags: ${{ matrix.module }}
          name: dl-engine-${{ matrix.module }}

  # 构建测试
  build:
    name: 构建测试
    runs-on: ubuntu-latest
    needs: [lint-and-format, test]
    strategy:
      matrix:
        module: [engine, editor]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build ${{ matrix.module }}
        run: |
          cd dl-engine/${{ matrix.module }}
          npm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: dl-engine-${{ matrix.module }}-build
          path: dl-engine/${{ matrix.module }}/dist/

  # 服务器端测试
  test-server:
    name: 服务器端测试
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test_password
          MYSQL_DATABASE: dl_engine_test
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

      postgres:
        image: pgvector/pgvector:pg16
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: dl_vector_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd="pg_isready"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    strategy:
      matrix:
        service: [gateway, auth, api, instance, media, storage, ai, task]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Test ${{ matrix.service }} service
        run: |
          cd dl-engine/server/${{ matrix.service }}
          npm run test
        env:
          NODE_ENV: test
          DATABASE_URL: mysql://root:test_password@localhost:3306/dl_engine_test
          REDIS_URL: redis://localhost:6379
          POSTGRES_URL: postgresql://postgres:test_password@localhost:5432/dl_vector_test

  # Docker 构建测试
  docker-build:
    name: Docker 构建测试
    runs-on: ubuntu-latest
    needs: [build, test-server]
    if: github.event_name == 'push'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./dl-engine/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # 部署到开发环境
  deploy-dev:
    name: 部署到开发环境
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.ref == 'refs/heads/dev' && github.event_name == 'push'
    environment: development
    steps:
      - name: Deploy to development
        run: |
          echo "部署到开发环境..."
          # 这里添加实际的部署脚本

  # 部署到生产环境
  deploy-prod:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    steps:
      - name: Deploy to production
        run: |
          echo "部署到生产环境..."
          # 这里添加实际的部署脚本
