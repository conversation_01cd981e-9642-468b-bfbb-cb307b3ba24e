/*
CPAL-1.0 License
*/

import { <PERSON>, All, Req, <PERSON><PERSON>, Param } from '@nestjs/common'
import { ApiTags, ApiOperation } from '@nestjs/swagger'
import { Request, Response } from 'express'
import { ProxyService } from './proxy.service'

/**
 * 代理控制器
 * 
 * 处理所有微服务的代理请求
 */
@ApiTags('proxy')
@Controller()
export class ProxyController {
  constructor(private readonly proxyService: ProxyService) {}

  /**
   * 认证服务代理
   */
  @All('auth/*')
  @ApiOperation({ summary: '认证服务代理' })
  async proxyAuth(@Req() req: Request, @Res() res: Response) {
    return this.proxyService.forward('auth', req, res)
  }

  /**
   * API 服务代理
   */
  @All('api/*')
  @ApiOperation({ summary: 'API 服务代理' })
  async proxyApi(@Req() req: Request, @Res() res: Response) {
    return this.proxyService.forward('api', req, res)
  }

  /**
   * 实例服务代理
   */
  @All('instance/*')
  @ApiOperation({ summary: '实例服务代理' })
  async proxyInstance(@Req() req: Request, @Res() res: Response) {
    return this.proxyService.forward('instance', req, res)
  }

  /**
   * 媒体服务代理
   */
  @All('media/*')
  @ApiOperation({ summary: '媒体服务代理' })
  async proxyMedia(@Req() req: Request, @Res() res: Response) {
    return this.proxyService.forward('media', req, res)
  }

  /**
   * 存储服务代理
   */
  @All('storage/*')
  @ApiOperation({ summary: '存储服务代理' })
  async proxyStorage(@Req() req: Request, @Res() res: Response) {
    return this.proxyService.forward('storage', req, res)
  }

  /**
   * 任务服务代理
   */
  @All('task/*')
  @ApiOperation({ summary: '任务服务代理' })
  async proxyTask(@Req() req: Request, @Res() res: Response) {
    return this.proxyService.forward('task', req, res)
  }

  /**
   * AI 服务代理
   */
  @All('ai/*')
  @ApiOperation({ summary: 'AI 服务代理' })
  async proxyAI(@Req() req: Request, @Res() res: Response) {
    return this.proxyService.forward('ai', req, res)
  }
}
