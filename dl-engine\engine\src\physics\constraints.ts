/*
CPAL-1.0 License
*/

import * as THREE from 'three'

/**
 * 约束类型枚举
 */
export enum ConstraintType {
  POINT_TO_POINT = 'point_to_point',
  HINGE = 'hinge',
  SLIDER = 'slider',
  CONE_TWIST = 'cone_twist',
  DISTANCE = 'distance',
  SPRING = 'spring'
}

/**
 * 约束配置接口
 */
export interface ConstraintConfig {
  type: ConstraintType
  entityA: number
  entityB: number
  pivotA?: THREE.Vector3
  pivotB?: THREE.Vector3
  axisA?: THREE.Vector3
  axisB?: THREE.Vector3
  distance?: number
  stiffness?: number
  damping?: number
  limits?: {
    min: number
    max: number
  }
}

/**
 * 约束实例接口
 */
export interface Constraint {
  id: string
  type: ConstraintType
  config: ConstraintConfig
  isActive: boolean
  constraint?: any // 物理引擎特定的约束对象
}

/**
 * 约束系统
 *
 * 管理物理约束的创建、更新和销毁
 */
export class ConstraintSystem {
  private constraints = new Map<string, Constraint>()
  private constraintCount = 0
  private nextConstraintId = 1

  /**
   * 初始化约束系统
   */
  async initialize(): Promise<void> {
    console.log('Constraint system initialized')
  }

  /**
   * 创建约束
   */
  createConstraint(config: ConstraintConfig): string {
    const id = `constraint_${this.nextConstraintId++}`

    const constraint: Constraint = {
      id,
      type: config.type,
      config,
      isActive: true
    }

    // 根据约束类型创建具体的约束
    constraint.constraint = this.createPhysicsConstraint(config)

    this.constraints.set(id, constraint)
    this.constraintCount++

    return id
  }

  /**
   * 创建物理约束对象
   */
  private createPhysicsConstraint(config: ConstraintConfig): any {
    // 这里应该根据当前使用的物理引擎创建相应的约束
    // 目前返回一个模拟对象
    return {
      type: config.type,
      entityA: config.entityA,
      entityB: config.entityB,
      enabled: true
    }
  }

  /**
   * 获取约束
   */
  getConstraint(id: string): Constraint | undefined {
    return this.constraints.get(id)
  }

  /**
   * 移除约束
   */
  removeConstraint(id: string): boolean {
    const constraint = this.constraints.get(id)
    if (!constraint) {
      return false
    }

    // 从物理世界中移除约束
    if (constraint.constraint) {
      this.destroyPhysicsConstraint(constraint.constraint)
    }

    this.constraints.delete(id)
    this.constraintCount--

    return true
  }

  /**
   * 销毁物理约束对象
   */
  private destroyPhysicsConstraint(physicsConstraint: any): void {
    // 这里应该调用物理引擎的约束销毁方法
    physicsConstraint.enabled = false
  }

  /**
   * 启用/禁用约束
   */
  setConstraintEnabled(id: string, enabled: boolean): boolean {
    const constraint = this.constraints.get(id)
    if (!constraint) {
      return false
    }

    constraint.isActive = enabled
    if (constraint.constraint) {
      constraint.constraint.enabled = enabled
    }

    return true
  }

  /**
   * 更新约束参数
   */
  updateConstraint(id: string, updates: Partial<ConstraintConfig>): boolean {
    const constraint = this.constraints.get(id)
    if (!constraint) {
      return false
    }

    // 更新配置
    constraint.config = { ...constraint.config, ...updates }

    // 重新创建物理约束
    if (constraint.constraint) {
      this.destroyPhysicsConstraint(constraint.constraint)
      constraint.constraint = this.createPhysicsConstraint(constraint.config)
    }

    return true
  }

  /**
   * 获取实体的所有约束
   */
  getEntityConstraints(entityId: number): Constraint[] {
    const entityConstraints: Constraint[] = []

    for (const constraint of this.constraints.values()) {
      if (constraint.config.entityA === entityId || constraint.config.entityB === entityId) {
        entityConstraints.push(constraint)
      }
    }

    return entityConstraints
  }

  /**
   * 移除实体的所有约束
   */
  removeEntityConstraints(entityId: number): number {
    const constraintsToRemove: string[] = []

    for (const constraint of this.constraints.values()) {
      if (constraint.config.entityA === entityId || constraint.config.entityB === entityId) {
        constraintsToRemove.push(constraint.id)
      }
    }

    constraintsToRemove.forEach(id => this.removeConstraint(id))
    return constraintsToRemove.length
  }

  /**
   * 更新约束系统
   */
  update(deltaTime: number): void {
    // 更新所有活跃的约束
    for (const constraint of this.constraints.values()) {
      if (constraint.isActive && constraint.constraint) {
        this.updatePhysicsConstraint(constraint, deltaTime)
      }
    }
  }

  /**
   * 更新物理约束
   */
  private updatePhysicsConstraint(constraint: Constraint, deltaTime: number): void {
    // 这里可以实现约束的实时更新逻辑
    // 例如弹簧约束的阻尼计算等
  }

  /**
   * 获取约束数量
   */
  getConstraintCount(): number {
    return this.constraintCount
  }

  /**
   * 获取所有约束
   */
  getAllConstraints(): Map<string, Constraint> {
    return new Map(this.constraints)
  }

  /**
   * 获取约束统计信息
   */
  getConstraintStats(): Record<ConstraintType, number> {
    const stats: Record<ConstraintType, number> = {
      [ConstraintType.POINT_TO_POINT]: 0,
      [ConstraintType.HINGE]: 0,
      [ConstraintType.SLIDER]: 0,
      [ConstraintType.CONE_TWIST]: 0,
      [ConstraintType.DISTANCE]: 0,
      [ConstraintType.SPRING]: 0
    }

    for (const constraint of this.constraints.values()) {
      stats[constraint.type]++
    }

    return stats
  }

  /**
   * 销毁约束系统
   */
  async destroy(): Promise<void> {
    // 销毁所有约束
    for (const constraint of this.constraints.values()) {
      if (constraint.constraint) {
        this.destroyPhysicsConstraint(constraint.constraint)
      }
    }

    this.constraints.clear()
    this.constraintCount = 0
    this.nextConstraintId = 1

    console.log('Constraint system destroyed')
  }
}
