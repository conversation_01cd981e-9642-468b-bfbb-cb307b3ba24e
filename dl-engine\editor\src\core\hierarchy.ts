/*
CPAL-1.0 License
*/

import * as THREE from 'three'
import { SceneEditor } from './scene-editor'

/**
 * 层次面板
 * 
 * 显示场景对象的层次结构
 */
export class HierarchyPanel {
  private sceneEditor?: SceneEditor
  private container?: HTMLElement
  private treeContainer?: HTMLElement
  private selectedObjects = new Set<string>()

  // 回调函数
  public onObjectSelected?: (objectId: string, addToSelection: boolean) => void

  async initialize(): Promise<void> {
    this.createUI()
    console.log('Hierarchy panel initialized')
  }

  update(deltaTime: number): void {
    this.refreshHierarchy()
  }

  setSceneEditor(sceneEditor: SceneEditor): void {
    this.sceneEditor = sceneEditor
  }

  private createUI(): void {
    this.container = document.createElement('div')
    this.container.className = 'hierarchy-panel'
    this.container.innerHTML = `
      <div class="hierarchy-header">
        <h3>层次结构</h3>
        <button class="add-object-btn">+</button>
      </div>
      <div class="hierarchy-tree"></div>
    `

    this.treeContainer = this.container.querySelector('.hierarchy-tree') as HTMLElement

    const hierarchyContainer = document.getElementById('hierarchy-panel-container')
    if (hierarchyContainer) {
      hierarchyContainer.appendChild(this.container)
    }
  }

  private refreshHierarchy(): void {
    if (!this.treeContainer || !this.sceneEditor) return

    const scene = this.sceneEditor.getScene()
    if (!scene) return

    let html = ''
    scene.children.forEach(child => {
      if (child.userData.editorObject) {
        html += this.renderObjectNode(child, 0)
      }
    })

    this.treeContainer.innerHTML = html
    this.bindHierarchyEvents()
  }

  private renderObjectNode(object: THREE.Object3D, depth: number): string {
    const isSelected = this.selectedObjects.has(object.uuid)
    const hasChildren = object.children.length > 0
    
    let html = `
      <div class="hierarchy-node ${isSelected ? 'selected' : ''}" 
           data-object-id="${object.uuid}" 
           style="padding-left: ${depth * 20}px">
        ${hasChildren ? '<span class="expand-icon">▼</span>' : '<span class="expand-icon"></span>'}
        <span class="object-icon">${this.getObjectIcon(object)}</span>
        <span class="object-name">${object.name || 'Unnamed'}</span>
      </div>
    `

    // 渲染子对象
    object.children.forEach(child => {
      html += this.renderObjectNode(child, depth + 1)
    })

    return html
  }

  private getObjectIcon(object: THREE.Object3D): string {
    if (object instanceof THREE.Mesh) return '🎲'
    if (object instanceof THREE.Light) return '💡'
    if (object instanceof THREE.Camera) return '📷'
    if (object instanceof THREE.Group) return '📁'
    return '⚪'
  }

  private bindHierarchyEvents(): void {
    if (!this.treeContainer) return

    const nodes = this.treeContainer.querySelectorAll('.hierarchy-node')
    nodes.forEach(node => {
      node.addEventListener('click', (e) => {
        const objectId = node.getAttribute('data-object-id')
        if (objectId && this.onObjectSelected) {
          this.onObjectSelected(objectId, e.ctrlKey || e.metaKey)
        }
      })
    })
  }

  async destroy(): Promise<void> {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container)
    }
    console.log('Hierarchy panel destroyed')
  }
}
