{"name": "@dl-engine/engine", "version": "1.0.0", "description": "DL-Engine 核心引擎 - ECS架构、渲染、物理、网络同步、XR支持", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./src/*": "./src/*"}, "files": ["dist", "src"], "scripts": {"build": "tsc", "build:watch": "tsc --watch", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "check-errors": "tsc --noEmit", "validate": "npm run lint && npm run check-errors && npm run test"}, "keywords": ["dl-engine", "engine", "ecs", "three.js", "webgl", "physics", "rapier3d", "cannon-es", "webxr", "vr", "ar", "education", "learning"], "author": {"name": "Digital Learning Engine Team", "email": "<EMAIL>"}, "license": "CPAL-1.0", "repository": {"type": "git", "url": "git://github.com/dl-engine/dl-engine.git", "directory": "dl-engine/engine"}, "bugs": {"url": "https://github.com/dl-engine/dl-engine/issues"}, "homepage": "https://dl-engine.org", "dependencies": {"@dimforge/rapier3d-compat": "0.11.2", "@gltf-transform/core": "4.0.10", "@gltf-transform/extensions": "4.0.10", "@gltf-transform/functions": "4.0.10", "@tweenjs/tween.js": "^23.1.2", "bitecs": "github:NateTheGreatt/bitECS#rc-0-4-0", "cannon-es": "^0.20.0", "draco3dgltf": "^1.5.6", "fflate": "0.7.4", "hls.js": "^1.3.5", "js-sha3": "^0.8.0", "lodash": "4.17.21", "noisejs": "2.1.0", "postprocessing": "6.37.3", "property-graph": "^2.0.0", "rfc6902": "^5.1.2", "three": "0.176.0", "three-stdlib": "^2.32.2", "three.quarks": "^0.16.0", "troika-three-text": "^0.52.4", "ts-matches": "5.3.0", "uuid": "9.0.0"}, "devDependencies": {"@types/lodash": "^4.17.13", "@types/three": "0.176.0", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitest/coverage-istanbul": "2.1.1", "eslint": "9.5.0", "prettier": "3.0.2", "rimraf": "4.4.0", "typescript": "5.6.3", "vitest": "2.1.1"}, "peerDependencies": {"react": "18.2.0", "react-dom": "18.2.0"}, "engines": {"node": ">= 22.11.0"}}