{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "composite": true, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo", "lib": ["ESNext", "DOM", "DOM.Iterable"], "jsx": "react-jsx", "types": ["@types/react", "@types/react-dom", "@types/three", "@types/uuid", "@types/lodash"]}, "include": ["src/**/*", "public/**/*"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "**/*.stories.ts", "**/*.stories.tsx"], "references": [{"path": "../engine"}]}