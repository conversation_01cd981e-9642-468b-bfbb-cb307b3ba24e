{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "composite": true, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo", "lib": ["ESNext", "DOM", "DOM.Iterable", "WebWorker"], "types": ["@types/three", "@types/uuid", "@types/lodash"]}, "include": ["src/**/*"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.spec.ts"], "references": []}