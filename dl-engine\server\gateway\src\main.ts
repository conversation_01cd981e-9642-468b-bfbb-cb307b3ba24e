/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import { NestFactory } from '@nestjs/core'
import { ValidationPipe } from '@nestjs/common'
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger'
import { AppModule } from './app.module'
import { Logger } from '@nestjs/common'

/**
 * DL-Engine API 网关
 * 
 * 统一的 API 入口点，负责：
 * - 请求路由
 * - 负载均衡
 * - 认证授权
 * - 限流控制
 * - API 文档
 */
async function bootstrap() {
  const logger = new Logger('Gateway')
  
  const app = await NestFactory.create(AppModule)

  // 启用 CORS
  app.enableCors({
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
    credentials: true
  })

  // 全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true
  }))

  // API 文档配置
  const config = new DocumentBuilder()
    .setTitle('DL-Engine API Gateway')
    .setDescription('Digital Learning Engine API Gateway Documentation')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('auth', '认证服务')
    .addTag('api', 'API 服务')
    .addTag('instance', '实例服务')
    .addTag('media', '媒体服务')
    .addTag('storage', '存储服务')
    .addTag('task', '任务服务')
    .addTag('ai', 'AI 服务')
    .build()

  const document = SwaggerModule.createDocument(app, config)
  SwaggerModule.setup('api-docs', app, document)

  const port = process.env.PORT || 3030
  await app.listen(port)
  
  logger.log(`🚀 API Gateway is running on: http://localhost:${port}`)
  logger.log(`📚 API Documentation: http://localhost:${port}/api-docs`)
}

bootstrap().catch(err => {
  console.error('Failed to start API Gateway:', err)
  process.exit(1)
})
