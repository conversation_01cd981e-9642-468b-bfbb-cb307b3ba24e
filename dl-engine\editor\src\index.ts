/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

/**
 * DL-Engine 在线编辑器入口
 * 
 * 这是 Digital Learning Engine 的在线编辑器模块，提供：
 * - 可视化场景编辑
 * - 属性面板
 * - 资产管理
 * - 可视化脚本编程
 * - 多语言支持
 * - 插件系统
 */

// 核心模块导出
export * from './core'
export * from './ui'
export * from './visual-script'
export * from './plugins'

// 版本信息
export const VERSION = '1.0.0'
export const BUILD_DATE = new Date().toISOString()

console.log(`DL-Engine Editor v${VERSION} initialized at ${BUILD_DATE}`)
