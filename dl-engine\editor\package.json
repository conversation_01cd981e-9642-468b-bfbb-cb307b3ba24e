{"name": "@dl-engine/editor", "version": "1.0.0", "description": "DL-Engine 在线编辑器 - 可视化3D/VR/AR内容创作工具", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./src/*": "./src/*"}, "files": ["dist", "src", "public"], "scripts": {"dev": "vite", "build": "tsc && vite build", "build:watch": "tsc --watch", "preview": "vite preview", "clean": "<PERSON><PERSON><PERSON> dist", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "check-errors": "tsc --noEmit", "validate": "npm run lint && npm run check-errors && npm run test", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "keywords": ["dl-engine", "editor", "3d-editor", "vr-editor", "ar-editor", "visual-scripting", "education", "learning", "react", "antd", "three.js"], "author": {"name": "Digital Learning Engine Team", "email": "<EMAIL>"}, "license": "CPAL-1.0", "repository": {"type": "git", "url": "git://github.com/dl-engine/dl-engine.git", "directory": "dl-engine/editor"}, "bugs": {"url": "https://github.com/dl-engine/dl-engine/issues"}, "homepage": "https://dl-engine.org", "dependencies": {"@dl-engine/engine": "workspace:*", "@ant-design/colors": "^7.1.0", "@ant-design/icons": "^5.5.1", "@hookstate/core": "4.0.1", "@reduxjs/toolkit": "^2.3.0", "antd": "^5.22.2", "i18next": "21.6.16", "i18next-browser-languagedetector": "6.1.3", "lodash": "4.17.21", "react": "18.2.0", "react-dom": "18.2.0", "react-dnd": "16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-i18next": "11.16.6", "react-redux": "^9.1.2", "react-router-dom": "6.9.0", "redux": "^5.0.1", "three": "0.176.0", "uuid": "9.0.0"}, "devDependencies": {"@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/react-vite": "^8.4.7", "@storybook/test": "^8.4.7", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "15.0.4", "@testing-library/user-event": "^14.5.2", "@types/lodash": "^4.17.13", "@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "@types/three": "0.176.0", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitejs/plugin-react": "4.3.1", "@vitest/coverage-istanbul": "2.1.1", "eslint": "9.5.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "jsdom": "25.0.1", "prettier": "3.0.2", "rimraf": "4.4.0", "sass": "1.59.3", "storybook": "^8.4.7", "typescript": "5.6.3", "vite": "5.4.8", "vitest": "2.1.1"}, "engines": {"node": ">= 22.11.0"}}