/*
CPAL-1.0 License
*/

import * as THREE from 'three'

/**
 * 碰撞事件接口
 */
export interface CollisionEvent {
  entityA: number
  entityB: number
  point: THREE.Vector3
  normal: THREE.Vector3
  impulse: number
  timestamp: number
}

/**
 * 碰撞回调类型
 */
export type CollisionCallback = (event: CollisionEvent) => void

/**
 * 碰撞层配置
 */
export interface CollisionLayer {
  name: string
  mask: number
  collidesWith: number[]
}

/**
 * 碰撞检测系统
 *
 * 管理碰撞事件和回调
 */
export class CollisionDetection {
  private collisionCount = 0
  private collisionEvents: CollisionEvent[] = []
  private collisionCallbacks = new Map<string, CollisionCallback[]>()
  private collisionLayers = new Map<string, CollisionLayer>()
  private entityLayers = new Map<number, string>()

  /**
   * 初始化碰撞检测系统
   */
  async initialize(): Promise<void> {
    // 设置默认碰撞层
    this.setupDefaultLayers()

    console.log('Collision detection initialized')
  }

  /**
   * 设置默认碰撞层
   */
  private setupDefaultLayers(): void {
    this.addCollisionLayer('default', 1, [1, 2, 4, 8])
    this.addCollisionLayer('player', 2, [1, 4, 8])
    this.addCollisionLayer('enemy', 4, [1, 2, 8])
    this.addCollisionLayer('environment', 8, [1, 2, 4])
  }

  /**
   * 添加碰撞层
   */
  addCollisionLayer(name: string, mask: number, collidesWith: number[]): void {
    this.collisionLayers.set(name, {
      name,
      mask,
      collidesWith
    })
  }

  /**
   * 设置实体碰撞层
   */
  setEntityLayer(entityId: number, layerName: string): void {
    if (this.collisionLayers.has(layerName)) {
      this.entityLayers.set(entityId, layerName)
    }
  }

  /**
   * 获取实体碰撞层
   */
  getEntityLayer(entityId: number): string | undefined {
    return this.entityLayers.get(entityId)
  }

  /**
   * 检查两个实体是否应该碰撞
   */
  shouldCollide(entityA: number, entityB: number): boolean {
    const layerA = this.entityLayers.get(entityA)
    const layerB = this.entityLayers.get(entityB)

    if (!layerA || !layerB) {
      return true // 默认碰撞
    }

    const configA = this.collisionLayers.get(layerA)
    const configB = this.collisionLayers.get(layerB)

    if (!configA || !configB) {
      return true
    }

    return configA.collidesWith.includes(configB.mask)
  }

  /**
   * 添加碰撞事件
   */
  addCollisionEvent(event: CollisionEvent): void {
    this.collisionEvents.push(event)
    this.collisionCount++

    // 触发回调
    this.triggerCollisionCallbacks(event)
  }

  /**
   * 注册碰撞回调
   */
  onCollision(eventType: string, callback: CollisionCallback): void {
    if (!this.collisionCallbacks.has(eventType)) {
      this.collisionCallbacks.set(eventType, [])
    }
    this.collisionCallbacks.get(eventType)!.push(callback)
  }

  /**
   * 移除碰撞回调
   */
  offCollision(eventType: string, callback: CollisionCallback): void {
    const callbacks = this.collisionCallbacks.get(eventType)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index !== -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 触发碰撞回调
   */
  private triggerCollisionCallbacks(event: CollisionEvent): void {
    // 触发通用碰撞回调
    const generalCallbacks = this.collisionCallbacks.get('collision')
    if (generalCallbacks) {
      generalCallbacks.forEach(callback => callback(event))
    }

    // 触发特定实体的碰撞回调
    const entityACallbacks = this.collisionCallbacks.get(`entity_${event.entityA}`)
    if (entityACallbacks) {
      entityACallbacks.forEach(callback => callback(event))
    }

    const entityBCallbacks = this.collisionCallbacks.get(`entity_${event.entityB}`)
    if (entityBCallbacks) {
      entityBCallbacks.forEach(callback => callback(event))
    }
  }

  /**
   * 更新碰撞检测系统
   */
  update(deltaTime: number): void {
    // 清理旧的碰撞事件
    const currentTime = Date.now()
    this.collisionEvents = this.collisionEvents.filter(
      event => currentTime - event.timestamp < 1000 // 保留1秒内的事件
    )
  }

  /**
   * 获取碰撞数量
   */
  getCollisionCount(): number {
    return this.collisionCount
  }

  /**
   * 获取最近的碰撞事件
   */
  getRecentCollisions(timeWindow: number = 100): CollisionEvent[] {
    const currentTime = Date.now()
    return this.collisionEvents.filter(
      event => currentTime - event.timestamp <= timeWindow
    )
  }

  /**
   * 获取实体的碰撞事件
   */
  getEntityCollisions(entityId: number, timeWindow: number = 100): CollisionEvent[] {
    return this.getRecentCollisions(timeWindow).filter(
      event => event.entityA === entityId || event.entityB === entityId
    )
  }

  /**
   * 清除碰撞历史
   */
  clearCollisionHistory(): void {
    this.collisionEvents.length = 0
    this.collisionCount = 0
  }

  /**
   * 销毁碰撞检测系统
   */
  async destroy(): Promise<void> {
    this.collisionEvents.length = 0
    this.collisionCallbacks.clear()
    this.collisionLayers.clear()
    this.entityLayers.clear()
    this.collisionCount = 0

    console.log('Collision detection destroyed')
  }
}
