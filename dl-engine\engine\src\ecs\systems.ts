/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import { EntityManager } from './entities'
import { ComponentManager } from './components'

/**
 * 系统优先级枚举
 */
export enum SystemPriority {
  HIGHEST = 0,
  HIGH = 100,
  NORMAL = 200,
  LOW = 300,
  LOWEST = 400
}

/**
 * 系统阶段枚举
 */
export enum SystemPhase {
  PRE_UPDATE = 'preUpdate',
  UPDATE = 'update',
  POST_UPDATE = 'postUpdate',
  RENDER = 'render'
}

/**
 * 基础系统接口
 */
export interface System {
  name: string
  priority: SystemPriority
  phase: SystemPhase
  enabled: boolean
  requiredComponents: string[]
  
  initialize?(entityManager: EntityManager, componentManager: ComponentManager): Promise<void>
  update(deltaTime: number, entities: number[]): void
  destroy?(): Promise<void>
}

/**
 * 输入系统
 */
export class InputSystem implements System {
  name = 'InputSystem'
  priority = SystemPriority.HIGHEST
  phase = SystemPhase.PRE_UPDATE
  enabled = true
  requiredComponents = ['input']

  update(deltaTime: number, entities: number[]): void {
    // 处理输入逻辑
    for (const entityId of entities) {
      // 更新输入组件
    }
  }
}

/**
 * 模拟系统
 */
export class SimulationSystem implements System {
  name = 'SimulationSystem'
  priority = SystemPriority.NORMAL
  phase = SystemPhase.UPDATE
  enabled = true
  requiredComponents = ['transform', 'physics']

  update(deltaTime: number, entities: number[]): void {
    // 处理物理模拟
    for (const entityId of entities) {
      // 更新物理和变换
    }
  }
}

/**
 * 动画系统
 */
export class AnimationSystem implements System {
  name = 'AnimationSystem'
  priority = SystemPriority.NORMAL
  phase = SystemPhase.UPDATE
  enabled = true
  requiredComponents = ['animation', 'transform']

  update(deltaTime: number, entities: number[]): void {
    // 处理动画逻辑
    for (const entityId of entities) {
      // 更新动画状态
    }
  }
}

/**
 * 表现系统
 */
export class PresentationSystem implements System {
  name = 'PresentationSystem'
  priority = SystemPriority.LOW
  phase = SystemPhase.RENDER
  enabled = true
  requiredComponents = ['transform', 'mesh']

  update(deltaTime: number, entities: number[]): void {
    // 处理渲染表现
    for (const entityId of entities) {
      // 更新渲染状态
    }
  }
}

/**
 * 系统管理器
 * 
 * 负责系统的注册、调度和执行
 */
export class SystemManager {
  private systems = new Map<string, System>()
  private systemsByPhase = new Map<SystemPhase, System[]>()
  private entityManager?: EntityManager
  private componentManager?: ComponentManager
  private isRunning = false

  /**
   * 初始化系统管理器
   */
  async initialize(): Promise<void> {
    // 注册默认系统
    this.registerSystem(new InputSystem())
    this.registerSystem(new SimulationSystem())
    this.registerSystem(new AnimationSystem())
    this.registerSystem(new PresentationSystem())
    
    console.log('System manager initialized')
  }

  /**
   * 设置实体管理器引用
   */
  setEntityManager(entityManager: EntityManager): void {
    this.entityManager = entityManager
  }

  /**
   * 设置组件管理器引用
   */
  setComponentManager(componentManager: ComponentManager): void {
    this.componentManager = componentManager
  }

  /**
   * 注册系统
   */
  registerSystem(system: System): void {
    this.systems.set(system.name, system)
    this.organizeSystemsByPhase()
    
    // 初始化系统
    if (system.initialize && this.entityManager && this.componentManager) {
      system.initialize(this.entityManager, this.componentManager)
    }
  }

  /**
   * 获取系统
   */
  getSystem(name: string): System | undefined {
    return this.systems.get(name)
  }

  /**
   * 移除系统
   */
  removeSystem(name: string): boolean {
    const system = this.systems.get(name)
    if (system) {
      if (system.destroy) {
        system.destroy()
      }
      this.systems.delete(name)
      this.organizeSystemsByPhase()
      return true
    }
    return false
  }

  /**
   * 启用/禁用系统
   */
  setSystemEnabled(name: string, enabled: boolean): boolean {
    const system = this.systems.get(name)
    if (system) {
      system.enabled = enabled
      return true
    }
    return false
  }

  /**
   * 启动系统管理器
   */
  start(): void {
    this.isRunning = true
  }

  /**
   * 停止系统管理器
   */
  stop(): void {
    this.isRunning = false
  }

  /**
   * 更新所有系统
   */
  update(deltaTime: number): void {
    if (!this.isRunning || !this.componentManager) {
      return
    }

    // 按阶段执行系统
    for (const phase of [
      SystemPhase.PRE_UPDATE,
      SystemPhase.UPDATE,
      SystemPhase.POST_UPDATE,
      SystemPhase.RENDER
    ]) {
      this.updatePhase(phase, deltaTime)
    }
  }

  /**
   * 更新指定阶段的系统
   */
  private updatePhase(phase: SystemPhase, deltaTime: number): void {
    const systems = this.systemsByPhase.get(phase)
    if (!systems || !this.componentManager) {
      return
    }

    for (const system of systems) {
      if (!system.enabled) {
        continue
      }

      try {
        // 查询符合条件的实体
        const entities = this.componentManager.queryEntities(system.requiredComponents)
        
        // 执行系统更新
        system.update(deltaTime, entities)
      } catch (error) {
        console.error(`Error in system ${system.name}:`, error)
      }
    }
  }

  /**
   * 按阶段组织系统
   */
  private organizeSystemsByPhase(): void {
    this.systemsByPhase.clear()

    for (const system of this.systems.values()) {
      if (!this.systemsByPhase.has(system.phase)) {
        this.systemsByPhase.set(system.phase, [])
      }
      this.systemsByPhase.get(system.phase)!.push(system)
    }

    // 按优先级排序每个阶段的系统
    for (const systems of this.systemsByPhase.values()) {
      systems.sort((a, b) => a.priority - b.priority)
    }
  }

  /**
   * 获取系统数量
   */
  getSystemCount(): number {
    return this.systems.size
  }

  /**
   * 获取所有系统
   */
  getAllSystems(): Map<string, System> {
    return new Map(this.systems)
  }

  /**
   * 获取系统统计信息
   */
  getSystemStats(): {
    total: number
    enabled: number
    disabled: number
    byPhase: Record<SystemPhase, number>
  } {
    let enabled = 0
    let disabled = 0
    const byPhase: Record<SystemPhase, number> = {
      [SystemPhase.PRE_UPDATE]: 0,
      [SystemPhase.UPDATE]: 0,
      [SystemPhase.POST_UPDATE]: 0,
      [SystemPhase.RENDER]: 0
    }

    for (const system of this.systems.values()) {
      if (system.enabled) {
        enabled++
      } else {
        disabled++
      }
      byPhase[system.phase]++
    }

    return {
      total: this.systems.size,
      enabled,
      disabled,
      byPhase
    }
  }

  /**
   * 销毁系统管理器
   */
  async destroy(): Promise<void> {
    this.stop()

    // 销毁所有系统
    for (const system of this.systems.values()) {
      if (system.destroy) {
        await system.destroy()
      }
    }

    this.systems.clear()
    this.systemsByPhase.clear()
    this.entityManager = undefined
    this.componentManager = undefined

    console.log('System manager destroyed')
  }
}
