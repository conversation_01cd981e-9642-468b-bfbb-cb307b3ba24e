/*
CPAL-1.0 License
*/

import { EditorState } from './editor'
import { EditorTool } from './scene-editor'

/**
 * 编辑器工具栏
 * 
 * 提供编辑器的主要工具和控制按钮
 */
export class EditorToolbar {
  private container?: HTMLElement
  private currentTool = EditorTool.SELECT
  private editorState = EditorState.IDLE

  // 回调函数
  public onToolChanged?: (tool: EditorTool) => void
  public onPlayClicked?: () => void
  public onPauseClicked?: () => void
  public onStopClicked?: () => void

  async initialize(): Promise<void> {
    this.createUI()
    console.log('Editor toolbar initialized')
  }

  update(deltaTime: number): void {
    this.updateButtonStates()
  }

  onStateChange(oldState: EditorState, newState: EditorState): void {
    this.editorState = newState
    this.updateButtonStates()
  }

  setTool(tool: EditorTool): void {
    this.currentTool = tool
    this.updateToolButtons()
  }

  private createUI(): void {
    this.container = document.createElement('div')
    this.container.className = 'editor-toolbar'
    this.container.innerHTML = `
      <div class="toolbar-section tools">
        <button class="tool-btn active" data-tool="select" title="选择工具">🔍</button>
        <button class="tool-btn" data-tool="move" title="移动工具">↔️</button>
        <button class="tool-btn" data-tool="rotate" title="旋转工具">🔄</button>
        <button class="tool-btn" data-tool="scale" title="缩放工具">📏</button>
      </div>
      
      <div class="toolbar-section playback">
        <button class="play-btn" title="播放">▶️</button>
        <button class="pause-btn" title="暂停">⏸️</button>
        <button class="stop-btn" title="停止">⏹️</button>
      </div>
      
      <div class="toolbar-section primitives">
        <button class="primitive-btn" data-primitive="cube" title="立方体">🎲</button>
        <button class="primitive-btn" data-primitive="sphere" title="球体">⚪</button>
        <button class="primitive-btn" data-primitive="cylinder" title="圆柱体">🥫</button>
        <button class="primitive-btn" data-primitive="plane" title="平面">⬜</button>
      </div>
      
      <div class="toolbar-section actions">
        <button class="action-btn" data-action="save" title="保存">💾</button>
        <button class="action-btn" data-action="undo" title="撤销">↶</button>
        <button class="action-btn" data-action="redo" title="重做">↷</button>
      </div>
    `

    this.bindEvents()

    const toolbarContainer = document.getElementById('editor-toolbar-container')
    if (toolbarContainer) {
      toolbarContainer.appendChild(this.container)
    }
  }

  private bindEvents(): void {
    if (!this.container) return

    // 工具按钮
    const toolButtons = this.container.querySelectorAll('.tool-btn')
    toolButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const tool = btn.getAttribute('data-tool') as EditorTool
        if (tool) {
          this.setTool(tool)
          if (this.onToolChanged) {
            this.onToolChanged(tool)
          }
        }
      })
    })

    // 播放控制按钮
    const playBtn = this.container.querySelector('.play-btn')
    const pauseBtn = this.container.querySelector('.pause-btn')
    const stopBtn = this.container.querySelector('.stop-btn')

    playBtn?.addEventListener('click', () => {
      if (this.onPlayClicked) this.onPlayClicked()
    })

    pauseBtn?.addEventListener('click', () => {
      if (this.onPauseClicked) this.onPauseClicked()
    })

    stopBtn?.addEventListener('click', () => {
      if (this.onStopClicked) this.onStopClicked()
    })

    // 基础几何体按钮
    const primitiveButtons = this.container.querySelectorAll('.primitive-btn')
    primitiveButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const primitive = btn.getAttribute('data-primitive')
        if (primitive) {
          this.createPrimitive(primitive as 'cube' | 'sphere' | 'cylinder' | 'plane')
        }
      })
    })

    // 动作按钮
    const actionButtons = this.container.querySelectorAll('.action-btn')
    actionButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const action = btn.getAttribute('data-action')
        if (action) {
          this.executeAction(action)
        }
      })
    })
  }

  private updateToolButtons(): void {
    if (!this.container) return

    const toolButtons = this.container.querySelectorAll('.tool-btn')
    toolButtons.forEach(btn => {
      const tool = btn.getAttribute('data-tool')
      btn.classList.toggle('active', tool === this.currentTool)
    })
  }

  private updateButtonStates(): void {
    if (!this.container) return

    const playBtn = this.container.querySelector('.play-btn') as HTMLButtonElement
    const pauseBtn = this.container.querySelector('.pause-btn') as HTMLButtonElement
    const stopBtn = this.container.querySelector('.stop-btn') as HTMLButtonElement

    if (playBtn) playBtn.disabled = this.editorState === EditorState.PLAYING
    if (pauseBtn) pauseBtn.disabled = this.editorState !== EditorState.PLAYING
    if (stopBtn) stopBtn.disabled = this.editorState === EditorState.IDLE || this.editorState === EditorState.EDITING
  }

  private createPrimitive(type: 'cube' | 'sphere' | 'cylinder' | 'plane'): void {
    // 这里应该调用场景编辑器创建基础几何体
    console.log(`Creating primitive: ${type}`)
  }

  private executeAction(action: string): void {
    switch (action) {
      case 'save':
        console.log('Save action')
        break
      case 'undo':
        console.log('Undo action')
        break
      case 'redo':
        console.log('Redo action')
        break
    }
  }

  async destroy(): Promise<void> {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container)
    }
    console.log('Editor toolbar destroyed')
  }
}
