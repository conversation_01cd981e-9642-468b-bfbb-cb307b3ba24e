{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ESNext", "DOM", "DOM.Iterable"], "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": false, "isolatedModules": true, "moduleDetection": "force", "noEmit": false, "allowJs": true, "forceConsistentCasingInFileNames": true, "strict": true, "strictNullChecks": true, "strictBindCallApply": true, "noImplicitAny": true, "noUnusedLocals": false, "noUnusedParameters": false, "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "jsx": "react-jsx", "experimentalDecorators": true, "emitDecoratorMetadata": true, "declaration": true, "declarationMap": true, "baseUrl": ".", "paths": {"@dl-engine/engine": ["./dl-engine/engine/src"], "@dl-engine/engine/*": ["./dl-engine/engine/src/*"], "@dl-engine/editor": ["./dl-engine/editor/src"], "@dl-engine/editor/*": ["./dl-engine/editor/src/*"]}}, "include": ["./__global.d.ts"], "exclude": ["node_modules", "dist", "build", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}