/*
CPAL-1.0 License
*/

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus
} from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiQuery
} from '@nestjs/swagger'
import { ScenesService } from './scenes.service'
import { JwtAuthGuard } from '../auth/jwt-auth.guard'

/**
 * 创建场景DTO
 */
export class CreateSceneDto {
  name: string
  description?: string
  projectId: string
  template?: string
  settings?: Record<string, any>
}

/**
 * 更新场景DTO
 */
export class UpdateSceneDto {
  name?: string
  description?: string
  settings?: Record<string, any>
  sceneData?: any
}

/**
 * 场景查询DTO
 */
export class SceneQueryDto {
  projectId?: string
  page?: number = 1
  limit?: number = 10
  search?: string
  sortBy?: 'name' | 'createdAt' | 'updatedAt' = 'updatedAt'
  sortOrder?: 'asc' | 'desc' = 'desc'
}

@ApiTags('scenes')
@Controller('scenes')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ScenesController {
  constructor(private readonly scenesService: ScenesService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '创建新场景' })
  @ApiBody({ type: CreateSceneDto })
  @ApiResponse({ status: 201, description: '场景创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 403, description: '无权限' })
  async createScene(
    @Body() createSceneDto: CreateSceneDto,
    @Request() req: any
  ) {
    return this.scenesService.create(createSceneDto, req.user.sub)
  }

  @Get()
  @ApiOperation({ summary: '获取场景列表' })
  @ApiQuery({ type: SceneQueryDto })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getScenes(
    @Query() query: SceneQueryDto,
    @Request() req: any
  ) {
    return this.scenesService.findAll(query, req.user.sub)
  }

  @Get(':id')
  @ApiOperation({ summary: '获取场景详情' })
  @ApiParam({ name: 'id', description: '场景ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '场景不存在' })
  @ApiResponse({ status: 403, description: '无权限' })
  async getScene(
    @Param('id') id: string,
    @Request() req: any
  ) {
    return this.scenesService.findOne(id, req.user.sub)
  }

  @Put(':id')
  @ApiOperation({ summary: '更新场景' })
  @ApiParam({ name: 'id', description: '场景ID' })
  @ApiBody({ type: UpdateSceneDto })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '场景不存在' })
  @ApiResponse({ status: 403, description: '无权限' })
  async updateScene(
    @Param('id') id: string,
    @Body() updateSceneDto: UpdateSceneDto,
    @Request() req: any
  ) {
    return this.scenesService.update(id, updateSceneDto, req.user.sub)
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除场景' })
  @ApiParam({ name: 'id', description: '场景ID' })
  @ApiResponse({ status: 204, description: '删除成功' })
  @ApiResponse({ status: 404, description: '场景不存在' })
  @ApiResponse({ status: 403, description: '无权限' })
  async deleteScene(
    @Param('id') id: string,
    @Request() req: any
  ) {
    return this.scenesService.remove(id, req.user.sub)
  }

  @Post(':id/duplicate')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '复制场景' })
  @ApiParam({ name: 'id', description: '场景ID' })
  @ApiResponse({ status: 201, description: '复制成功' })
  @ApiResponse({ status: 404, description: '场景不存在' })
  @ApiResponse({ status: 403, description: '无权限' })
  async duplicateScene(
    @Param('id') id: string,
    @Body() body: { name?: string; projectId?: string },
    @Request() req: any
  ) {
    return this.scenesService.duplicate(id, req.user.sub, body.name, body.projectId)
  }

  @Post(':id/export')
  @ApiOperation({ summary: '导出场景' })
  @ApiParam({ name: 'id', description: '场景ID' })
  @ApiResponse({ status: 200, description: '导出成功' })
  @ApiResponse({ status: 404, description: '场景不存在' })
  @ApiResponse({ status: 403, description: '无权限' })
  async exportScene(
    @Param('id') id: string,
    @Body() body: { format?: 'gltf' | 'fbx' | 'obj' },
    @Request() req: any
  ) {
    return this.scenesService.exportScene(id, req.user.sub, body.format || 'gltf')
  }

  @Post('import')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '导入场景' })
  @ApiResponse({ status: 201, description: '导入成功' })
  @ApiResponse({ status: 400, description: '导入失败' })
  async importScene(
    @Body() body: { 
      projectId: string
      sceneData: any
      name?: string
      description?: string
    },
    @Request() req: any
  ) {
    return this.scenesService.importScene(body, req.user.sub)
  }

  @Get(':id/preview')
  @ApiOperation({ summary: '获取场景预览' })
  @ApiParam({ name: 'id', description: '场景ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getScenePreview(
    @Param('id') id: string,
    @Query('width') width: number = 400,
    @Query('height') height: number = 300,
    @Request() req: any
  ) {
    return this.scenesService.generatePreview(id, req.user.sub, width, height)
  }

  @Post(':id/publish')
  @ApiOperation({ summary: '发布场景' })
  @ApiParam({ name: 'id', description: '场景ID' })
  @ApiResponse({ status: 200, description: '发布成功' })
  async publishScene(
    @Param('id') id: string,
    @Body() body: { version?: string; notes?: string },
    @Request() req: any
  ) {
    return this.scenesService.publishScene(id, req.user.sub, body.version, body.notes)
  }

  @Get(':id/versions')
  @ApiOperation({ summary: '获取场景版本历史' })
  @ApiParam({ name: 'id', description: '场景ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getSceneVersions(
    @Param('id') id: string,
    @Request() req: any
  ) {
    return this.scenesService.getVersionHistory(id, req.user.sub)
  }

  @Post(':id/versions/:version/restore')
  @ApiOperation({ summary: '恢复到指定版本' })
  @ApiParam({ name: 'id', description: '场景ID' })
  @ApiParam({ name: 'version', description: '版本号' })
  @ApiResponse({ status: 200, description: '恢复成功' })
  async restoreVersion(
    @Param('id') id: string,
    @Param('version') version: string,
    @Request() req: any
  ) {
    return this.scenesService.restoreVersion(id, version, req.user.sub)
  }
}
