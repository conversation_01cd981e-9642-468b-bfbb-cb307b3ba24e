/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import * as THREE from 'three'
import { SceneEditor } from './scene-editor'

/**
 * 属性类型枚举
 */
export enum PropertyType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  VECTOR3 = 'vector3',
  COLOR = 'color',
  TEXTURE = 'texture',
  MATERIAL = 'material',
  ENUM = 'enum'
}

/**
 * 属性定义接口
 */
export interface PropertyDefinition {
  key: string
  label: string
  type: PropertyType
  value: any
  min?: number
  max?: number
  step?: number
  options?: string[]
  readonly?: boolean
}

/**
 * 属性组接口
 */
export interface PropertyGroup {
  name: string
  expanded: boolean
  properties: PropertyDefinition[]
}

/**
 * 属性面板
 * 
 * 显示和编辑选中对象的属性
 */
export class PropertiesPanel {
  private sceneEditor?: SceneEditor
  private selectedObjects: string[] = []
  private propertyGroups: PropertyGroup[] = []
  
  // UI 元素
  private container?: HTMLElement
  private propertyElements = new Map<string, HTMLElement>()

  /**
   * 初始化属性面板
   */
  async initialize(): Promise<void> {
    this.createUI()
    console.log('Properties panel initialized')
  }

  /**
   * 更新属性面板
   */
  update(deltaTime: number): void {
    // 更新属性值显示
    this.updatePropertyValues()
  }

  /**
   * 设置场景编辑器引用
   */
  setSceneEditor(sceneEditor: SceneEditor): void {
    this.sceneEditor = sceneEditor
  }

  /**
   * 设置选中的对象
   */
  setSelectedObjects(objectIds: string[]): void {
    this.selectedObjects = objectIds
    this.refreshProperties()
  }

  /**
   * 获取选中的对象
   */
  getSelectedObjects(): string[] {
    return [...this.selectedObjects]
  }

  /**
   * 刷新属性显示
   */
  refreshProperties(): void {
    this.propertyGroups = []
    
    if (this.selectedObjects.length === 0) {
      this.showEmptyState()
      return
    }

    if (this.selectedObjects.length === 1) {
      this.showSingleObjectProperties()
    } else {
      this.showMultiObjectProperties()
    }

    this.renderProperties()
  }

  /**
   * 创建 UI
   */
  private createUI(): void {
    this.container = document.createElement('div')
    this.container.className = 'properties-panel'
    this.container.innerHTML = `
      <div class="properties-header">
        <h3>属性</h3>
      </div>
      <div class="properties-content">
        <div class="empty-state">
          <p>请选择一个对象以查看其属性</p>
        </div>
      </div>
    `

    // 添加到 DOM（这里假设有一个属性面板容器）
    const panelContainer = document.getElementById('properties-panel-container')
    if (panelContainer) {
      panelContainer.appendChild(this.container)
    }
  }

  /**
   * 显示空状态
   */
  private showEmptyState(): void {
    if (!this.container) return

    const content = this.container.querySelector('.properties-content')
    if (content) {
      content.innerHTML = `
        <div class="empty-state">
          <p>请选择一个对象以查看其属性</p>
        </div>
      `
    }
  }

  /**
   * 显示单个对象属性
   */
  private showSingleObjectProperties(): void {
    const objectId = this.selectedObjects[0]
    const object = this.getObjectById(objectId)
    
    if (!object) return

    // 基础属性组
    this.propertyGroups.push({
      name: '基础属性',
      expanded: true,
      properties: [
        {
          key: 'name',
          label: '名称',
          type: PropertyType.STRING,
          value: object.name
        },
        {
          key: 'visible',
          label: '可见',
          type: PropertyType.BOOLEAN,
          value: object.visible
        }
      ]
    })

    // 变换属性组
    this.propertyGroups.push({
      name: '变换',
      expanded: true,
      properties: [
        {
          key: 'position',
          label: '位置',
          type: PropertyType.VECTOR3,
          value: object.position
        },
        {
          key: 'rotation',
          label: '旋转',
          type: PropertyType.VECTOR3,
          value: object.rotation
        },
        {
          key: 'scale',
          label: '缩放',
          type: PropertyType.VECTOR3,
          value: object.scale
        }
      ]
    })

    // 如果是网格对象，添加材质属性
    if (object instanceof THREE.Mesh) {
      this.addMeshProperties(object)
    }

    // 如果是光源，添加光源属性
    if (object instanceof THREE.Light) {
      this.addLightProperties(object)
    }
  }

  /**
   * 显示多个对象属性
   */
  private showMultiObjectProperties(): void {
    // 显示多选对象的公共属性
    this.propertyGroups.push({
      name: '公共属性',
      expanded: true,
      properties: [
        {
          key: 'visible',
          label: '可见',
          type: PropertyType.BOOLEAN,
          value: this.getCommonPropertyValue('visible')
        }
      ]
    })

    this.propertyGroups.push({
      name: '变换',
      expanded: true,
      properties: [
        {
          key: 'position',
          label: '位置',
          type: PropertyType.VECTOR3,
          value: this.getCommonVector3Value('position')
        },
        {
          key: 'rotation',
          label: '旋转',
          type: PropertyType.VECTOR3,
          value: this.getCommonVector3Value('rotation')
        },
        {
          key: 'scale',
          label: '缩放',
          type: PropertyType.VECTOR3,
          value: this.getCommonVector3Value('scale')
        }
      ]
    })
  }

  /**
   * 添加网格属性
   */
  private addMeshProperties(mesh: THREE.Mesh): void {
    const material = mesh.material as THREE.Material

    this.propertyGroups.push({
      name: '材质',
      expanded: false,
      properties: [
        {
          key: 'material.color',
          label: '颜色',
          type: PropertyType.COLOR,
          value: (material as any).color || new THREE.Color(0xffffff)
        },
        {
          key: 'material.transparent',
          label: '透明',
          type: PropertyType.BOOLEAN,
          value: material.transparent
        },
        {
          key: 'material.opacity',
          label: '不透明度',
          type: PropertyType.NUMBER,
          value: material.opacity,
          min: 0,
          max: 1,
          step: 0.01
        }
      ]
    })
  }

  /**
   * 添加光源属性
   */
  private addLightProperties(light: THREE.Light): void {
    this.propertyGroups.push({
      name: '光源',
      expanded: true,
      properties: [
        {
          key: 'color',
          label: '颜色',
          type: PropertyType.COLOR,
          value: light.color
        },
        {
          key: 'intensity',
          label: '强度',
          type: PropertyType.NUMBER,
          value: light.intensity,
          min: 0,
          max: 10,
          step: 0.1
        }
      ]
    })
  }

  /**
   * 渲染属性
   */
  private renderProperties(): void {
    if (!this.container) return

    const content = this.container.querySelector('.properties-content')
    if (!content) return

    let html = ''

    for (const group of this.propertyGroups) {
      html += `
        <div class="property-group ${group.expanded ? 'expanded' : ''}">
          <div class="property-group-header" onclick="this.parentElement.classList.toggle('expanded')">
            <span class="expand-icon">${group.expanded ? '▼' : '▶'}</span>
            <span class="group-name">${group.name}</span>
          </div>
          <div class="property-group-content">
            ${this.renderPropertyList(group.properties)}
          </div>
        </div>
      `
    }

    content.innerHTML = html
    this.bindPropertyEvents()
  }

  /**
   * 渲染属性列表
   */
  private renderPropertyList(properties: PropertyDefinition[]): string {
    let html = ''

    for (const prop of properties) {
      html += `
        <div class="property-item" data-key="${prop.key}">
          <label class="property-label">${prop.label}</label>
          <div class="property-input">
            ${this.renderPropertyInput(prop)}
          </div>
        </div>
      `
    }

    return html
  }

  /**
   * 渲染属性输入控件
   */
  private renderPropertyInput(prop: PropertyDefinition): string {
    switch (prop.type) {
      case PropertyType.STRING:
        return `<input type="text" value="${prop.value}" ${prop.readonly ? 'readonly' : ''}>`
      
      case PropertyType.NUMBER:
        return `<input type="number" value="${prop.value}" 
                min="${prop.min || ''}" max="${prop.max || ''}" step="${prop.step || 'any'}"
                ${prop.readonly ? 'readonly' : ''}>`
      
      case PropertyType.BOOLEAN:
        return `<input type="checkbox" ${prop.value ? 'checked' : ''} ${prop.readonly ? 'disabled' : ''}>`
      
      case PropertyType.VECTOR3:
        const vec = prop.value as THREE.Vector3
        return `
          <div class="vector3-input">
            <input type="number" value="${vec.x}" step="0.01" data-component="x">
            <input type="number" value="${vec.y}" step="0.01" data-component="y">
            <input type="number" value="${vec.z}" step="0.01" data-component="z">
          </div>
        `
      
      case PropertyType.COLOR:
        const color = prop.value as THREE.Color
        return `<input type="color" value="#${color.getHexString()}">`
      
      case PropertyType.ENUM:
        let options = ''
        if (prop.options) {
          for (const option of prop.options) {
            options += `<option value="${option}" ${prop.value === option ? 'selected' : ''}>${option}</option>`
          }
        }
        return `<select ${prop.readonly ? 'disabled' : ''}>${options}</select>`
      
      default:
        return `<input type="text" value="${prop.value}" readonly>`
    }
  }

  /**
   * 绑定属性事件
   */
  private bindPropertyEvents(): void {
    if (!this.container) return

    const propertyItems = this.container.querySelectorAll('.property-item')
    
    propertyItems.forEach(item => {
      const key = item.getAttribute('data-key')
      if (!key) return

      const inputs = item.querySelectorAll('input, select')
      inputs.forEach(input => {
        input.addEventListener('change', (event) => {
          this.handlePropertyChange(key, event.target as HTMLInputElement)
        })
      })
    })
  }

  /**
   * 处理属性变化
   */
  private handlePropertyChange(key: string, input: HTMLInputElement): void {
    const objects = this.selectedObjects.map(id => this.getObjectById(id)).filter(obj => obj)
    
    if (objects.length === 0) return

    for (const object of objects) {
      this.setObjectProperty(object, key, input)
    }
  }

  /**
   * 设置对象属性
   */
  private setObjectProperty(object: THREE.Object3D, key: string, input: HTMLInputElement): void {
    const keys = key.split('.')
    let target: any = object

    // 导航到目标属性
    for (let i = 0; i < keys.length - 1; i++) {
      target = target[keys[i]]
      if (!target) return
    }

    const finalKey = keys[keys.length - 1]
    
    if (input.type === 'checkbox') {
      target[finalKey] = input.checked
    } else if (input.type === 'number') {
      target[finalKey] = parseFloat(input.value)
    } else if (input.type === 'color') {
      if (target[finalKey] instanceof THREE.Color) {
        target[finalKey].setHex(parseInt(input.value.replace('#', ''), 16))
      }
    } else {
      target[finalKey] = input.value
    }
  }

  /**
   * 更新属性值显示
   */
  private updatePropertyValues(): void {
    // 实时更新属性值显示
  }

  /**
   * 根据 ID 获取对象
   */
  private getObjectById(id: string): THREE.Object3D | null {
    if (!this.sceneEditor) return null
    
    const scene = this.sceneEditor.getScene()
    if (!scene) return null

    return scene.getObjectByProperty('uuid', id) || null
  }

  /**
   * 获取公共属性值
   */
  private getCommonPropertyValue(key: string): any {
    const objects = this.selectedObjects.map(id => this.getObjectById(id)).filter(obj => obj)
    
    if (objects.length === 0) return undefined

    const firstValue = (objects[0] as any)[key]
    const allSame = objects.every(obj => (obj as any)[key] === firstValue)
    
    return allSame ? firstValue : '混合值'
  }

  /**
   * 获取公共 Vector3 值
   */
  private getCommonVector3Value(key: string): THREE.Vector3 {
    const objects = this.selectedObjects.map(id => this.getObjectById(id)).filter(obj => obj)
    
    if (objects.length === 0) return new THREE.Vector3()

    const firstValue = (objects[0] as any)[key] as THREE.Vector3
    const allSame = objects.every(obj => {
      const vec = (obj as any)[key] as THREE.Vector3
      return vec.equals(firstValue)
    })
    
    return allSame ? firstValue.clone() : new THREE.Vector3(NaN, NaN, NaN)
  }

  /**
   * 销毁属性面板
   */
  async destroy(): Promise<void> {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container)
    }
    
    this.propertyElements.clear()
    this.selectedObjects = []
    this.propertyGroups = []

    console.log('Properties panel destroyed')
  }
}
