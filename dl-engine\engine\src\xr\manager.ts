/*
CPAL-1.0 License
*/

import { XRConfig } from '../core/config'
import { WebXRIntegration } from './webxr'
import { XRUI<PERSON>enderer } from './ui'
import { XRInteraction } from './interaction'

/**
 * XR 管理器
 */
export class XRManager {
  private config: XRConfig
  private webxr: WebXRIntegration
  private ui: XRUIRenderer
  private interaction: XRInteraction
  private isInitialized = false

  constructor(config: XRConfig) {
    this.config = config
    this.webxr = new WebXRIntegration()
    this.ui = new XRUIRenderer()
    this.interaction = new XRInteraction()
  }

  async initialize(): Promise<void> {
    if (!this.config.enabled) {
      console.log('XR system disabled')
      return
    }

    await this.webxr.initialize()
    await this.ui.initialize()
    await this.interaction.initialize()

    this.isInitialized = true
    console.log('XR manager initialized')
  }

  update(deltaTime: number): void {
    if (!this.isInitialized) return

    this.webxr.update(deltaTime)
    this.ui.update(deltaTime)
    this.interaction.update(deltaTime)
  }

  async destroy(): Promise<void> {
    await this.interaction.destroy()
    await this.ui.destroy()
    await this.webxr.destroy()

    this.isInitialized = false
    console.log('XR manager destroyed')
  }
}
