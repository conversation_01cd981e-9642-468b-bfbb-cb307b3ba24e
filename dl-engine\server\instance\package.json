{"name": "@dl-engine/server-instance", "version": "1.0.0", "description": "DL-Engine 实例服务 - 世界实例、网络同步、物理同步、扩缩容", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "build": "tsc", "build:watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "check-errors": "tsc --noEmit", "validate": "npm run lint && npm run check-errors && npm run test"}, "keywords": ["dl-engine", "instance", "world-instance", "networking", "physics", "rapier3d", "websocket", "webrtc", "scaling"], "author": {"name": "Digital Learning Engine Team", "email": "<EMAIL>"}, "license": "CPAL-1.0", "dependencies": {"@dimforge/rapier3d-compat": "0.11.2", "@feathersjs/feathers": "5.0.5", "@feathersjs/koa": "5.0.5", "@feathersjs/socketio": "5.0.5", "bitecs": "github:NateTheGreatt/bitECS#rc-0-4-0", "ioredis": "^5.4.1", "mediasoup": "^3.15.16", "primus": "^8.0.9", "socket.io": "^4.8.1", "uuid": "9.0.0", "ws": "^8.18.0", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22.11.0", "@types/uuid": "^9.0.8", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitest/coverage-istanbul": "2.1.1", "eslint": "9.5.0", "prettier": "3.0.2", "rimraf": "4.4.0", "ts-node-dev": "^2.0.0", "typescript": "5.6.3", "vitest": "2.1.1"}, "engines": {"node": ">= 22.11.0"}}