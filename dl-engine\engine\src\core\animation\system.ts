/*
CPAL-1.0 License
*/

import * as THREE from 'three'

/**
 * 动画系统
 */
export class AnimationSystem {
  private mixer?: THREE.AnimationMixer
  private actions = new Map<string, THREE.AnimationAction>()
  private clips = new Map<string, THREE.AnimationClip>()

  constructor() {
    this.mixer = new THREE.AnimationMixer(new THREE.Object3D())
  }

  /**
   * 添加动画剪辑
   */
  addClip(name: string, clip: THREE.AnimationClip): void {
    this.clips.set(name, clip)
  }

  /**
   * 创建动画动作
   */
  createAction(name: string, clipName: string): THREE.AnimationAction | undefined {
    const clip = this.clips.get(clipName)
    if (!clip || !this.mixer) return undefined

    const action = this.mixer.clipAction(clip)
    this.actions.set(name, action)
    return action
  }

  /**
   * 播放动画
   */
  play(name: string): void {
    const action = this.actions.get(name)
    if (action) {
      action.play()
    }
  }

  /**
   * 停止动画
   */
  stop(name: string): void {
    const action = this.actions.get(name)
    if (action) {
      action.stop()
    }
  }

  /**
   * 更新动画系统
   */
  update(deltaTime: number): void {
    if (this.mixer) {
      this.mixer.update(deltaTime)
    }
  }

  /**
   * 销毁动画系统
   */
  destroy(): void {
    this.actions.clear()
    this.clips.clear()
    this.mixer = undefined
  }
}
