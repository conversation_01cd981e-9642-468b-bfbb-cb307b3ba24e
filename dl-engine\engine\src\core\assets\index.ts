/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

/**
 * DL-Engine 资产管理系统
 * 
 * 提供统一的资产加载、缓存和管理功能：
 * - GLTF/GLB 模型加载
 * - 纹理加载和管理
 * - 音频资产管理
 * - 流式加载
 * - 缓存策略
 */

export * from './manager'
export * from './loaders'
export * from './cache'
export * from './streaming'

// 主要类导出
export { AssetManager } from './manager'
export { GLTFLoader } from './loaders'
export { AssetCache } from './cache'
export { StreamingManager } from './streaming'
