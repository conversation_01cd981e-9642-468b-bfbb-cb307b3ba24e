/*
CPAL-1.0 License
*/

import { Injectable, Logger } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { Request, Response } from 'express'
import { LoadBalancerService } from './load-balancer.service'
import * as httpProxy from 'http-proxy-middleware'

/**
 * 代理服务
 * 
 * 负责请求转发和路由
 */
@Injectable()
export class ProxyService {
  private readonly logger = new Logger(ProxyService.name)
  private readonly serviceUrls = new Map<string, string>()

  constructor(
    private readonly configService: ConfigService,
    private readonly loadBalancer: LoadBalancerService
  ) {
    this.initializeServiceUrls()
  }

  /**
   * 转发请求到指定服务
   */
  async forward(serviceName: string, req: Request, res: Response): Promise<void> {
    try {
      const targetUrl = await this.getServiceUrl(serviceName)
      
      if (!targetUrl) {
        res.status(503).json({
          error: 'Service Unavailable',
          message: `Service ${serviceName} is not available`
        })
        return
      }

      const proxy = httpProxy.createProxyMiddleware({
        target: targetUrl,
        changeOrigin: true,
        pathRewrite: {
          [`^/${serviceName}`]: ''
        },
        onError: (err, req, res) => {
          this.logger.error(`Proxy error for ${serviceName}:`, err)
          res.status(502).json({
            error: 'Bad Gateway',
            message: 'Service temporarily unavailable'
          })
        },
        onProxyReq: (proxyReq, req, res) => {
          this.logger.debug(`Proxying ${req.method} ${req.url} to ${targetUrl}`)
        }
      })

      proxy(req, res, (err) => {
        if (err) {
          this.logger.error(`Proxy middleware error:`, err)
          res.status(500).json({
            error: 'Internal Server Error',
            message: 'Proxy configuration error'
          })
        }
      })
    } catch (error) {
      this.logger.error(`Error forwarding request to ${serviceName}:`, error)
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to process request'
      })
    }
  }

  /**
   * 获取服务 URL
   */
  private async getServiceUrl(serviceName: string): Promise<string | null> {
    // 使用负载均衡器获取最佳服务实例
    const instance = await this.loadBalancer.getHealthyInstance(serviceName)
    return instance ? instance.url : this.serviceUrls.get(serviceName) || null
  }

  /**
   * 初始化服务 URL 映射
   */
  private initializeServiceUrls(): void {
    const services = [
      'auth', 'api', 'instance', 'media', 
      'storage', 'task', 'ai'
    ]

    for (const service of services) {
      const url = this.configService.get(`${service.toUpperCase()}_SERVICE_URL`) || 
                  `http://localhost:${this.getDefaultPort(service)}`
      this.serviceUrls.set(service, url)
    }

    this.logger.log('Service URLs initialized:', Object.fromEntries(this.serviceUrls))
  }

  /**
   * 获取服务默认端口
   */
  private getDefaultPort(serviceName: string): number {
    const portMap = {
      auth: 3031,
      api: 3032,
      instance: 3033,
      media: 3034,
      storage: 3035,
      task: 3036,
      ai: 3037
    }
    return portMap[serviceName] || 3000
  }

  /**
   * 健康检查
   */
  async healthCheck(serviceName: string): Promise<boolean> {
    try {
      const url = this.serviceUrls.get(serviceName)
      if (!url) return false

      // 这里应该实现实际的健康检查逻辑
      // 例如发送 HTTP 请求到服务的健康检查端点
      return true
    } catch (error) {
      this.logger.error(`Health check failed for ${serviceName}:`, error)
      return false
    }
  }

  /**
   * 获取所有服务状态
   */
  async getServicesStatus(): Promise<Record<string, boolean>> {
    const status: Record<string, boolean> = {}
    
    for (const serviceName of this.serviceUrls.keys()) {
      status[serviceName] = await this.healthCheck(serviceName)
    }
    
    return status
  }
}
