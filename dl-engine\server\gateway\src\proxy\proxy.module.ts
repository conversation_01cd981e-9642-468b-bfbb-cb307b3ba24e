/*
CPAL-1.0 License
*/

import { Module } from '@nestjs/common'
import { ProxyController } from './proxy.controller'
import { ProxyService } from './proxy.service'
import { LoadBalancerService } from './load-balancer.service'

/**
 * 代理模块
 * 
 * 负责请求转发和负载均衡
 */
@Module({
  controllers: [ProxyController],
  providers: [ProxyService, LoadBalancerService],
  exports: [ProxyService]
})
export class ProxyModule {}
