/*
CPAL-1.0 License
*/

import { NestFactory } from '@nestjs/core'
import { AppModule } from './app.module'
import { Logger } from '@nestjs/common'

async function bootstrap() {
  const logger = new Logger('InstanceService')
  const app = await NestFactory.create(AppModule)
  
  const port = process.env.PORT || 3033
  await app.listen(port)
  logger.log(`🏠 Instance Service is running on: http://localhost:${port}`)
}

bootstrap()
