/*
CPAL-1.0 License
*/

import { Controller, Get } from '@nestjs/common'
import { ApiTags, ApiOperation } from '@nestjs/swagger'
import { MetricsService } from './metrics.service'

@ApiTags('metrics')
@Controller('metrics')
export class MetricsController {
  constructor(private readonly metricsService: MetricsService) {}

  @Get()
  @ApiOperation({ summary: '获取系统指标' })
  getMetrics() {
    return this.metricsService.getMetrics()
  }
}
