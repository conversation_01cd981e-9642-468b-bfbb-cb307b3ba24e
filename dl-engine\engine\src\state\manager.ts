/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import { StateStore } from './store'
import { ActionSystem } from './actions'
import { StateSync } from './sync'
import { StatePersistence } from './persistence'

/**
 * 状态管理器
 * 
 * 统一管理应用状态的主要类
 */
export class StateManager {
  private store: StateStore
  private actionSystem: ActionSystem
  private stateSync: StateSync
  private persistence: StatePersistence

  private isInitialized = false

  constructor() {
    this.store = new StateStore()
    this.actionSystem = new ActionSystem()
    this.stateSync = new StateSync()
    this.persistence = new StatePersistence()
  }

  /**
   * 初始化状态管理器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.warn('State manager is already initialized')
      return
    }

    try {
      // 初始化各个子系统
      await this.store.initialize()
      await this.actionSystem.initialize()
      await this.stateSync.initialize()
      await this.persistence.initialize()

      // 设置子系统之间的依赖关系
      this.setupDependencies()

      this.isInitialized = true
      console.log('State manager initialized')
    } catch (error) {
      console.error('Failed to initialize state manager:', error)
      throw error
    }
  }

  /**
   * 更新状态管理器
   */
  update(deltaTime: number): void {
    if (!this.isInitialized) {
      return
    }

    // 更新各个子系统
    this.actionSystem.update(deltaTime)
    this.stateSync.update(deltaTime)
    this.persistence.update(deltaTime)
  }

  /**
   * 获取状态
   */
  getState<T>(path: string): T | undefined {
    return this.store.getState<T>(path)
  }

  /**
   * 设置状态
   */
  setState<T>(path: string, value: T): void {
    this.store.setState(path, value)
  }

  /**
   * 订阅状态变化
   */
  subscribe(path: string, callback: (value: any) => void): () => void {
    return this.store.subscribe(path, callback)
  }

  /**
   * 派发动作
   */
  dispatch(action: any): void {
    this.actionSystem.dispatch(action)
  }

  /**
   * 获取状态存储
   */
  getStore(): StateStore {
    return this.store
  }

  /**
   * 获取动作系统
   */
  getActionSystem(): ActionSystem {
    return this.actionSystem
  }

  /**
   * 获取状态同步
   */
  getStateSync(): StateSync {
    return this.stateSync
  }

  /**
   * 获取持久化系统
   */
  getPersistence(): StatePersistence {
    return this.persistence
  }

  /**
   * 保存状态到持久化存储
   */
  async saveState(): Promise<void> {
    const state = this.store.getFullState()
    await this.persistence.save(state)
  }

  /**
   * 从持久化存储加载状态
   */
  async loadState(): Promise<void> {
    const state = await this.persistence.load()
    if (state) {
      this.store.setFullState(state)
    }
  }

  /**
   * 重置状态
   */
  resetState(): void {
    this.store.reset()
  }

  /**
   * 获取状态快照
   */
  getSnapshot(): any {
    return this.store.getSnapshot()
  }

  /**
   * 恢复状态快照
   */
  restoreSnapshot(snapshot: any): void {
    this.store.restoreSnapshot(snapshot)
  }

  /**
   * 设置子系统之间的依赖关系
   */
  private setupDependencies(): void {
    // 设置动作系统对状态存储的引用
    this.actionSystem.setStore(this.store)

    // 设置状态同步对状态存储的引用
    this.stateSync.setStore(this.store)

    // 设置持久化系统对状态存储的引用
    this.persistence.setStore(this.store)
  }

  /**
   * 销毁状态管理器
   */
  async destroy(): Promise<void> {
    // 保存当前状态
    await this.saveState()

    // 销毁各个子系统
    await this.persistence.destroy()
    await this.stateSync.destroy()
    await this.actionSystem.destroy()
    await this.store.destroy()

    this.isInitialized = false
    console.log('State manager destroyed')
  }
}
