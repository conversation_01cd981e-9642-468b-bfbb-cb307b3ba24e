/*
CPAL-1.0 License
*/

import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { MediaModule } from './media/media.module'
import { StreamingModule } from './streaming/streaming.module'
import { HealthModule } from './health/health.module'

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    MediaModule,
    StreamingModule,
    HealthModule
  ]
})
export class AppModule {}
