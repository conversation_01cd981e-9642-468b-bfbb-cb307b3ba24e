/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import * as THREE from 'three'
import { RenderingConfig } from '../config'
import { MaterialManager } from './materials'
import { LightingSystem } from './lighting'
import { ShadowSystem } from './shadows'
import { PostProcessingPipeline } from './postprocessing'

/**
 * 渲染统计信息
 */
export interface RenderStats {
  triangles: number
  drawCalls: number
  geometries: number
  textures: number
  programs: number
  memory: {
    geometries: number
    textures: number
  }
}

/**
 * DL-Engine 渲染系统
 * 
 * 基于 Three.js 的高性能渲染器
 */
export class RenderingSystem {
  private config: RenderingConfig
  private renderer?: THREE.WebGLRenderer
  private canvas?: HTMLCanvasElement
  private scene?: THREE.Scene
  private camera?: THREE.Camera

  // 子系统
  private materialManager: MaterialManager
  private lightingSystem: LightingSystem
  private shadowSystem: ShadowSystem
  private postProcessing: PostProcessingPipeline

  // 性能监控
  private stats: RenderStats = {
    triangles: 0,
    drawCalls: 0,
    geometries: 0,
    textures: 0,
    programs: 0,
    memory: {
      geometries: 0,
      textures: 0
    }
  }

  constructor(config: RenderingConfig) {
    this.config = config
    this.materialManager = new MaterialManager()
    this.lightingSystem = new LightingSystem()
    this.shadowSystem = new ShadowSystem()
    this.postProcessing = new PostProcessingPipeline()
  }

  /**
   * 初始化渲染系统
   */
  async initialize(canvas?: HTMLCanvasElement): Promise<void> {
    try {
      // 创建或获取 canvas
      this.canvas = canvas || this.createCanvas()

      // 创建渲染器
      this.renderer = new THREE.WebGLRenderer({
        canvas: this.canvas,
        antialias: this.config.antialias,
        alpha: true,
        powerPreference: 'high-performance'
      })

      // 配置渲染器
      this.configureRenderer()

      // 初始化子系统
      await this.materialManager.initialize(this.renderer)
      await this.lightingSystem.initialize()
      await this.shadowSystem.initialize(this.renderer)
      await this.postProcessing.initialize(this.renderer, this.canvas.width, this.canvas.height)

      // 设置窗口大小监听
      this.setupResizeHandler()

      console.log('Rendering system initialized')
    } catch (error) {
      console.error('Failed to initialize rendering system:', error)
      throw error
    }
  }

  /**
   * 渲染场景
   */
  render(scene: THREE.Scene, camera?: THREE.Camera): void {
    if (!this.renderer || !scene) {
      return
    }

    const activeCamera = camera || this.camera
    if (!activeCamera) {
      console.warn('No camera available for rendering')
      return
    }

    // 更新统计信息
    this.updateStats()

    // 更新光照系统
    this.lightingSystem.update(scene)

    // 更新阴影系统
    if (this.config.shadows) {
      this.shadowSystem.update(scene, activeCamera)
    }

    // 渲染
    if (this.config.postprocessing && this.postProcessing.isEnabled()) {
      this.postProcessing.render(scene, activeCamera)
    } else {
      this.renderer.render(scene, activeCamera)
    }
  }

  /**
   * 设置场景
   */
  setScene(scene: THREE.Scene): void {
    this.scene = scene
  }

  /**
   * 设置相机
   */
  setCamera(camera: THREE.Camera): void {
    this.camera = camera
  }

  /**
   * 获取渲染器
   */
  getRenderer(): THREE.WebGLRenderer | undefined {
    return this.renderer
  }

  /**
   * 获取 canvas
   */
  getCanvas(): HTMLCanvasElement | undefined {
    return this.canvas
  }

  /**
   * 获取渲染统计信息
   */
  getStats(): RenderStats {
    return { ...this.stats }
  }

  /**
   * 调整大小
   */
  resize(width: number, height: number): void {
    if (!this.renderer || !this.canvas) return

    this.canvas.width = width
    this.canvas.height = height
    this.renderer.setSize(width, height, false)

    // 更新后处理管道
    this.postProcessing.resize(width, height)

    // 更新相机
    if (this.camera && 'aspect' in this.camera) {
      (this.camera as THREE.PerspectiveCamera).aspect = width / height
      this.camera.updateProjectionMatrix()
    }
  }

  /**
   * 销毁渲染系统
   */
  async destroy(): Promise<void> {
    // 销毁子系统
    await this.postProcessing.destroy()
    await this.shadowSystem.destroy()
    await this.lightingSystem.destroy()
    await this.materialManager.destroy()

    // 销毁渲染器
    if (this.renderer) {
      this.renderer.dispose()
      this.renderer = undefined
    }

    this.canvas = undefined
    this.scene = undefined
    this.camera = undefined

    console.log('Rendering system destroyed')
  }

  /**
   * 创建 canvas 元素
   */
  private createCanvas(): HTMLCanvasElement {
    const canvas = document.createElement('canvas')
    canvas.width = window.innerWidth
    canvas.height = window.innerHeight
    canvas.style.display = 'block'
    document.body.appendChild(canvas)
    return canvas
  }

  /**
   * 配置渲染器
   */
  private configureRenderer(): void {
    if (!this.renderer) return

    // 基础设置
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, this.config.maxPixelRatio))
    this.renderer.setSize(this.canvas!.width, this.canvas!.height, false)
    this.renderer.outputColorSpace = THREE.SRGBColorSpace
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping
    this.renderer.toneMappingExposure = 1.0

    // 阴影设置
    if (this.config.shadows) {
      this.renderer.shadowMap.enabled = true
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
    }

    // 其他设置
    this.renderer.physicallyCorrectLights = true
    this.renderer.useLegacyLights = false
  }

  /**
   * 设置窗口大小监听
   */
  private setupResizeHandler(): void {
    const handleResize = () => {
      if (this.config.adaptiveResolution) {
        this.resize(window.innerWidth, window.innerHeight)
      }
    }

    window.addEventListener('resize', handleResize)
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    if (!this.renderer) return

    const info = this.renderer.info
    this.stats = {
      triangles: info.render.triangles,
      drawCalls: info.render.calls,
      geometries: info.memory.geometries,
      textures: info.memory.textures,
      programs: info.programs?.length || 0,
      memory: {
        geometries: info.memory.geometries,
        textures: info.memory.textures
      }
    }
  }
}
