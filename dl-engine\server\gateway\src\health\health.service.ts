/*
CPAL-1.0 License
*/

import { Injectable } from '@nestjs/common'

@Injectable()
export class HealthService {
  check() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    }
  }

  checkServices() {
    return {
      gateway: 'healthy',
      auth: 'healthy',
      api: 'healthy',
      instance: 'healthy',
      media: 'healthy',
      storage: 'healthy',
      task: 'healthy',
      ai: 'healthy'
    }
  }
}
