/*
CPAL-1.0 License
*/

import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { StorageModule } from './storage/storage.module'
import { FileModule } from './file/file.module'
import { HealthModule } from './health/health.module'

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    StorageModule,
    FileModule,
    HealthModule
  ]
})
export class AppModule {}
