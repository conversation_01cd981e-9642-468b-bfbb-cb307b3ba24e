/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import { EntityManager } from './entities'
import { ComponentManager } from './components'

/**
 * 网络消息类型
 */
export enum NetworkMessageType {
  ENTITY_CREATE = 'entity_create',
  ENTITY_DESTROY = 'entity_destroy',
  COMPONENT_ADD = 'component_add',
  COMPONENT_UPDATE = 'component_update',
  COMPONENT_REMOVE = 'component_remove',
  SNAPSHOT = 'snapshot',
  INPUT = 'input'
}

/**
 * 网络消息接口
 */
export interface NetworkMessage {
  type: NetworkMessageType
  timestamp: number
  data: any
}

/**
 * 实体创建消息
 */
export interface EntityCreateMessage extends NetworkMessage {
  type: NetworkMessageType.ENTITY_CREATE
  data: {
    entityId: number
    name?: string
  }
}

/**
 * 组件更新消息
 */
export interface ComponentUpdateMessage extends NetworkMessage {
  type: NetworkMessageType.COMPONENT_UPDATE
  data: {
    entityId: number
    componentType: string
    componentData: any
  }
}

/**
 * 快照消息
 */
export interface SnapshotMessage extends NetworkMessage {
  type: NetworkMessageType.SNAPSHOT
  data: {
    entities: Array<{
      id: number
      components: Record<string, any>
    }>
  }
}

/**
 * 网络同步配置
 */
export interface NetworkConfig {
  enabled: boolean
  serverUrl?: string
  updateRate: number
  snapshotRate: number
  interpolation: boolean
  prediction: boolean
}

/**
 * 网络系统
 * 
 * 负责 ECS 数据的网络同步
 */
export class NetworkingSystem {
  private config: NetworkConfig
  private entityManager?: EntityManager
  private componentManager?: ComponentManager
  private connection?: WebSocket
  private isConnected = false
  
  // 同步相关
  private lastSnapshotTime = 0
  private lastUpdateTime = 0
  private messageQueue: NetworkMessage[] = []
  private pendingMessages: NetworkMessage[] = []

  constructor() {
    this.config = {
      enabled: false,
      updateRate: 20, // 20 updates per second
      snapshotRate: 5,  // 5 snapshots per second
      interpolation: true,
      prediction: false
    }
  }

  /**
   * 初始化网络系统
   */
  async initialize(): Promise<void> {
    if (!this.config.enabled) {
      console.log('Networking system disabled')
      return
    }

    console.log('Networking system initialized')
  }

  /**
   * 设置实体管理器引用
   */
  setEntityManager(entityManager: EntityManager): void {
    this.entityManager = entityManager
  }

  /**
   * 设置组件管理器引用
   */
  setComponentManager(componentManager: ComponentManager): void {
    this.componentManager = componentManager
  }

  /**
   * 连接到服务器
   */
  async connect(serverUrl: string): Promise<void> {
    if (this.isConnected) {
      console.warn('Already connected to server')
      return
    }

    try {
      this.connection = new WebSocket(serverUrl)
      
      this.connection.onopen = () => {
        this.isConnected = true
        console.log('Connected to server:', serverUrl)
      }

      this.connection.onmessage = (event) => {
        this.handleMessage(JSON.parse(event.data))
      }

      this.connection.onclose = () => {
        this.isConnected = false
        console.log('Disconnected from server')
      }

      this.connection.onerror = (error) => {
        console.error('WebSocket error:', error)
      }

    } catch (error) {
      console.error('Failed to connect to server:', error)
      throw error
    }
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.connection) {
      this.connection.close()
      this.connection = undefined
    }
    this.isConnected = false
  }

  /**
   * 发送消息
   */
  sendMessage(message: NetworkMessage): void {
    if (!this.isConnected || !this.connection) {
      this.pendingMessages.push(message)
      return
    }

    try {
      this.connection.send(JSON.stringify(message))
    } catch (error) {
      console.error('Failed to send message:', error)
      this.pendingMessages.push(message)
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: NetworkMessage): void {
    this.messageQueue.push(message)
  }

  /**
   * 更新网络系统
   */
  update(deltaTime: number): void {
    if (!this.config.enabled) {
      return
    }

    const currentTime = Date.now()

    // 处理接收到的消息
    this.processIncomingMessages()

    // 发送待发送的消息
    this.sendPendingMessages()

    // 发送更新
    if (currentTime - this.lastUpdateTime >= 1000 / this.config.updateRate) {
      this.sendUpdates()
      this.lastUpdateTime = currentTime
    }

    // 发送快照
    if (currentTime - this.lastSnapshotTime >= 1000 / this.config.snapshotRate) {
      this.sendSnapshot()
      this.lastSnapshotTime = currentTime
    }
  }

  /**
   * 处理传入消息
   */
  private processIncomingMessages(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()!
      
      try {
        switch (message.type) {
          case NetworkMessageType.ENTITY_CREATE:
            this.handleEntityCreate(message as EntityCreateMessage)
            break
          case NetworkMessageType.COMPONENT_UPDATE:
            this.handleComponentUpdate(message as ComponentUpdateMessage)
            break
          case NetworkMessageType.SNAPSHOT:
            this.handleSnapshot(message as SnapshotMessage)
            break
          default:
            console.warn('Unknown message type:', message.type)
        }
      } catch (error) {
        console.error('Error processing message:', error)
      }
    }
  }

  /**
   * 处理实体创建
   */
  private handleEntityCreate(message: EntityCreateMessage): void {
    if (!this.entityManager) return

    const { entityId, name } = message.data
    // 注意：这里需要处理 ID 冲突
    this.entityManager.createEntity(name)
  }

  /**
   * 处理组件更新
   */
  private handleComponentUpdate(message: ComponentUpdateMessage): void {
    if (!this.componentManager) return

    const { entityId, componentType, componentData } = message.data
    this.componentManager.addComponent(entityId, componentType, componentData)
  }

  /**
   * 处理快照
   */
  private handleSnapshot(message: SnapshotMessage): void {
    if (!this.entityManager || !this.componentManager) return

    // 应用快照数据
    for (const entityData of message.data.entities) {
      for (const [componentType, componentData] of Object.entries(entityData.components)) {
        this.componentManager.addComponent(entityData.id, componentType, componentData)
      }
    }
  }

  /**
   * 发送待发送消息
   */
  private sendPendingMessages(): void {
    while (this.pendingMessages.length > 0 && this.isConnected) {
      const message = this.pendingMessages.shift()!
      this.sendMessage(message)
    }
  }

  /**
   * 发送更新
   */
  private sendUpdates(): void {
    // 发送组件更新
    // 这里需要实现脏标记系统来只发送变化的组件
  }

  /**
   * 发送快照
   */
  private sendSnapshot(): void {
    if (!this.entityManager || !this.componentManager) return

    const entities = this.entityManager.getActiveEntities()
    const snapshotData = entities.map(entityId => ({
      id: entityId,
      components: this.getEntitySnapshot(entityId)
    }))

    const snapshot: SnapshotMessage = {
      type: NetworkMessageType.SNAPSHOT,
      timestamp: Date.now(),
      data: { entities: snapshotData }
    }

    this.sendMessage(snapshot)
  }

  /**
   * 获取实体快照
   */
  private getEntitySnapshot(entityId: number): Record<string, any> {
    if (!this.componentManager) return {}

    const componentTypes = this.componentManager.getEntityComponents(entityId)
    const snapshot: Record<string, any> = {}

    for (const componentType of componentTypes) {
      const component = this.componentManager.getComponent(entityId, componentType)
      if (component) {
        snapshot[componentType] = component
      }
    }

    return snapshot
  }

  /**
   * 设置网络配置
   */
  setConfig(config: Partial<NetworkConfig>): void {
    this.config = { ...this.config, ...config }
  }

  /**
   * 获取网络配置
   */
  getConfig(): NetworkConfig {
    return { ...this.config }
  }

  /**
   * 检查是否已连接
   */
  isNetworkConnected(): boolean {
    return this.isConnected
  }

  /**
   * 销毁网络系统
   */
  async destroy(): Promise<void> {
    this.disconnect()
    this.messageQueue.length = 0
    this.pendingMessages.length = 0
    this.entityManager = undefined
    this.componentManager = undefined
    
    console.log('Networking system destroyed')
  }
}
