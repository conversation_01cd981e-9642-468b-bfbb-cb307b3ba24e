/*
CPAL-1.0 License
*/

import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { JwtModule } from '@nestjs/jwt'
import { PassportModule } from '@nestjs/passport'
import { AuthModule } from './auth/auth.module'
import { UserModule } from './user/user.module'
import { HealthModule } from './health/health.module'

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    PassportModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'dl-engine-secret',
      signOptions: { expiresIn: '24h' }
    }),
    AuthModule,
    UserModule,
    HealthModule
  ]
})
export class AppModule {}
