/*
CPAL-1.0 License
*/

import { Controller, Get } from '@nestjs/common'
import { ApiTags, ApiOperation } from '@nestjs/swagger'
import { HealthService } from './health.service'

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get()
  @ApiOperation({ summary: '健康检查' })
  check() {
    return this.healthService.check()
  }

  @Get('services')
  @ApiOperation({ summary: '服务状态检查' })
  checkServices() {
    return this.healthService.checkServices()
  }
}
