/*
CPAL-1.0 License
*/

import { StateStore } from './store'

/**
 * 状态同步
 */
export class StateSync {
  private store?: StateStore

  async initialize(): Promise<void> {
    console.log('State sync initialized')
  }

  setStore(store: StateStore): void {
    this.store = store
  }

  update(deltaTime: number): void {
    // 状态同步更新逻辑
  }

  async destroy(): Promise<void> {
    this.store = undefined
    console.log('State sync destroyed')
  }
}
