/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Digital Learning Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Digital Learning Engine team.

All portions of the code written by the Digital Learning Engine team are Copyright © 2021-2025
Digital Learning Engine. All Rights Reserved.
*/

import { EntityManager } from './entities'
import { ComponentManager } from './components'
import { SystemManager } from './systems'
import { NetworkingSystem } from './networking'

/**
 * ECS 世界
 * 
 * 管理所有实体、组件和系统的容器
 */
export class ECSWorld {
  private entityManager: EntityManager
  private componentManager: ComponentManager
  private systemManager: SystemManager
  private networkingSystem: NetworkingSystem

  private isInitialized = false
  private isRunning = false

  constructor() {
    this.entityManager = new EntityManager()
    this.componentManager = new ComponentManager()
    this.systemManager = new SystemManager()
    this.networkingSystem = new NetworkingSystem()
  }

  /**
   * 初始化 ECS 世界
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.warn('ECS World is already initialized')
      return
    }

    try {
      // 初始化各个管理器
      await this.entityManager.initialize()
      await this.componentManager.initialize()
      await this.systemManager.initialize()
      await this.networkingSystem.initialize()

      // 设置管理器之间的依赖关系
      this.setupDependencies()

      this.isInitialized = true
      console.log('ECS World initialized')
    } catch (error) {
      console.error('Failed to initialize ECS World:', error)
      throw error
    }
  }

  /**
   * 启动 ECS 世界
   */
  start(): void {
    if (!this.isInitialized) {
      throw new Error('ECS World must be initialized before starting')
    }

    if (this.isRunning) {
      console.warn('ECS World is already running')
      return
    }

    this.systemManager.start()
    this.isRunning = true
    console.log('ECS World started')
  }

  /**
   * 停止 ECS 世界
   */
  stop(): void {
    if (!this.isRunning) {
      return
    }

    this.systemManager.stop()
    this.isRunning = false
    console.log('ECS World stopped')
  }

  /**
   * 更新 ECS 世界
   */
  update(deltaTime: number): void {
    if (!this.isRunning) {
      return
    }

    // 更新系统管理器
    this.systemManager.update(deltaTime)

    // 更新网络系统
    this.networkingSystem.update(deltaTime)

    // 清理已销毁的实体
    this.entityManager.cleanup()
  }

  /**
   * 创建实体
   */
  createEntity(name?: string): number {
    return this.entityManager.createEntity(name)
  }

  /**
   * 销毁实体
   */
  destroyEntity(entityId: number): boolean {
    // 移除所有组件
    this.componentManager.removeAllComponents(entityId)
    
    // 销毁实体
    return this.entityManager.destroyEntity(entityId)
  }

  /**
   * 添加组件
   */
  addComponent<T>(entityId: number, componentType: string, data: T): void {
    this.componentManager.addComponent(entityId, componentType, data)
  }

  /**
   * 获取组件
   */
  getComponent<T>(entityId: number, componentType: string): T | undefined {
    return this.componentManager.getComponent<T>(entityId, componentType)
  }

  /**
   * 移除组件
   */
  removeComponent(entityId: number, componentType: string): boolean {
    return this.componentManager.removeComponent(entityId, componentType)
  }

  /**
   * 检查实体是否有组件
   */
  hasComponent(entityId: number, componentType: string): boolean {
    return this.componentManager.hasComponent(entityId, componentType)
  }

  /**
   * 查询实体
   */
  queryEntities(componentTypes: string[]): number[] {
    return this.componentManager.queryEntities(componentTypes)
  }

  /**
   * 注册系统
   */
  registerSystem(system: any): void {
    this.systemManager.registerSystem(system)
  }

  /**
   * 获取实体管理器
   */
  getEntityManager(): EntityManager {
    return this.entityManager
  }

  /**
   * 获取组件管理器
   */
  getComponentManager(): ComponentManager {
    return this.componentManager
  }

  /**
   * 获取系统管理器
   */
  getSystemManager(): SystemManager {
    return this.systemManager
  }

  /**
   * 获取网络系统
   */
  getNetworkingSystem(): NetworkingSystem {
    return this.networkingSystem
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    entities: number
    components: number
    systems: number
  } {
    return {
      entities: this.entityManager.getEntityCount(),
      components: this.componentManager.getComponentCount(),
      systems: this.systemManager.getSystemCount()
    }
  }

  /**
   * 销毁 ECS 世界
   */
  async destroy(): Promise<void> {
    this.stop()

    // 销毁所有管理器
    await this.networkingSystem.destroy()
    await this.systemManager.destroy()
    await this.componentManager.destroy()
    await this.entityManager.destroy()

    this.isInitialized = false
    console.log('ECS World destroyed')
  }

  /**
   * 设置管理器之间的依赖关系
   */
  private setupDependencies(): void {
    // 设置组件管理器对实体管理器的引用
    this.componentManager.setEntityManager(this.entityManager)

    // 设置系统管理器对其他管理器的引用
    this.systemManager.setEntityManager(this.entityManager)
    this.systemManager.setComponentManager(this.componentManager)

    // 设置网络系统的引用
    this.networkingSystem.setEntityManager(this.entityManager)
    this.networkingSystem.setComponentManager(this.componentManager)
  }

  /**
   * 检查是否已初始化
   */
  isWorldInitialized(): boolean {
    return this.isInitialized
  }

  /**
   * 检查是否正在运行
   */
  isWorldRunning(): boolean {
    return this.isRunning
  }
}
