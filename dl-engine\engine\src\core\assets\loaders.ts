/*
CPAL-1.0 License
*/

import * as THREE from 'three'
import { GLTFLoader as ThreeGLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js'
import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader.js'
import { MeshoptDecoder } from 'three/examples/jsm/libs/meshopt_decoder.module.js'

/**
 * GLTF 加载结果接口
 */
export interface GLTFResult {
  scene: THREE.Group
  scenes: THREE.Group[]
  animations: THREE.AnimationClip[]
  cameras: THREE.Camera[]
  asset: any
  parser: any
  userData: any
}

/**
 * 加载进度接口
 */
export interface LoaderProgress {
  loaded: number
  total: number
  percentage: number
}

/**
 * DL-Engine GLTF 加载器
 *
 * 增强的 GLTF 加载器，支持 Draco 压缩、KTX2 纹理等
 */
export class GLTFLoader {
  private loader: ThreeGLTFLoader
  private dracoLoader: DRACOLoader
  private ktx2Loader: KTX2Loader

  constructor() {
    this.loader = new ThreeGLTFLoader()
    this.dracoLoader = new DRACOLoader()
    this.ktx2Loader = new KTX2Loader()

    this.setupLoaders()
  }

  /**
   * 设置加载器
   */
  private setupLoaders(): void {
    // 设置 Draco 解码器
    this.dracoLoader.setDecoderPath('/draco/')
    this.dracoLoader.setDecoderConfig({ type: 'js' })
    this.loader.setDRACOLoader(this.dracoLoader)

    // 设置 KTX2 加载器
    this.ktx2Loader.setTranscoderPath('/basis/')
    this.loader.setKTX2Loader(this.ktx2Loader)

    // 设置 Meshopt 解码器
    this.loader.setMeshoptDecoder(MeshoptDecoder)
  }

  /**
   * 初始化加载器
   */
  async initialize(renderer?: THREE.WebGLRenderer): Promise<void> {
    if (renderer) {
      this.ktx2Loader.detectSupport(renderer)
    }
  }

  /**
   * 加载 GLTF 模型
   */
  async load(
    url: string,
    onProgress?: (progress: LoaderProgress) => void
  ): Promise<GLTFResult> {
    return new Promise((resolve, reject) => {
      this.loader.load(
        url,
        (gltf) => {
          // 处理加载结果
          this.processGLTF(gltf)
          resolve(gltf as GLTFResult)
        },
        (progress) => {
          if (onProgress) {
            onProgress({
              loaded: progress.loaded,
              total: progress.total,
              percentage: progress.total > 0 ? (progress.loaded / progress.total) * 100 : 0
            })
          }
        },
        reject
      )
    })
  }

  /**
   * 处理 GLTF 数据
   */
  private processGLTF(gltf: any): void {
    // 遍历场景中的所有对象
    gltf.scene.traverse((child: THREE.Object3D) => {
      // 设置阴影
      if (child instanceof THREE.Mesh) {
        child.castShadow = true
        child.receiveShadow = true

        // 优化材质
        if (child.material) {
          this.optimizeMaterial(child.material)
        }
      }

      // 设置用户数据
      child.userData.isGLTFObject = true
    })

    // 优化动画
    if (gltf.animations && gltf.animations.length > 0) {
      this.optimizeAnimations(gltf.animations)
    }
  }

  /**
   * 优化材质
   */
  private optimizeMaterial(material: THREE.Material | THREE.Material[]): void {
    const materials = Array.isArray(material) ? material : [material]

    materials.forEach((mat) => {
      if (mat instanceof THREE.MeshStandardMaterial) {
        // 启用环境遮蔽
        if (mat.aoMap) {
          mat.aoMapIntensity = 1.0
        }

        // 优化纹理设置
        this.optimizeTextures(mat)
      }
    })
  }

  /**
   * 优化纹理
   */
  private optimizeTextures(material: THREE.MeshStandardMaterial): void {
    const textures = [
      material.map,
      material.normalMap,
      material.roughnessMap,
      material.metalnessMap,
      material.aoMap,
      material.emissiveMap
    ]

    textures.forEach((texture) => {
      if (texture) {
        texture.generateMipmaps = true
        texture.minFilter = THREE.LinearMipmapLinearFilter
        texture.magFilter = THREE.LinearFilter
        texture.wrapS = THREE.RepeatWrapping
        texture.wrapT = THREE.RepeatWrapping
      }
    })
  }

  /**
   * 优化动画
   */
  private optimizeAnimations(animations: THREE.AnimationClip[]): void {
    animations.forEach((clip) => {
      // 优化关键帧
      clip.optimize()

      // 设置动画元数据
      clip.userData.optimized = true
    })
  }

  /**
   * 销毁加载器
   */
  dispose(): void {
    this.dracoLoader.dispose()
    this.ktx2Loader.dispose()
  }
}
