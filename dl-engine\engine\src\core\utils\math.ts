/*
CPAL-1.0 License
*/

import * as THREE from 'three'

/**
 * 数学工具类
 */
export class MathUtils {
  /**
   * 线性插值
   */
  static lerp(a: number, b: number, t: number): number {
    return a + (b - a) * t
  }

  /**
   * 钳制值
   */
  static clamp(value: number, min: number, max: number): number {
    return Math.max(min, Math.min(max, value))
  }

  /**
   * 角度转弧度
   */
  static degToRad(degrees: number): number {
    return degrees * (Math.PI / 180)
  }

  /**
   * 弧度转角度
   */
  static radToDeg(radians: number): number {
    return radians * (180 / Math.PI)
  }

  /**
   * 随机范围
   */
  static randomRange(min: number, max: number): number {
    return Math.random() * (max - min) + min
  }

  /**
   * 向量距离
   */
  static distance(a: THREE.Vector3, b: THREE.Vector3): number {
    return a.distanceTo(b)
  }

  /**
   * 向量插值
   */
  static lerpVector3(a: THREE.Vector3, b: THREE.Vector3, t: number): THREE.Vector3 {
    return new THREE.Vector3().lerpVectors(a, b, t)
  }
}
